//+------------------------------------------------------------------+
//|                                    PrecisionTrader_Functions.mqh |
//|                              Helper Functions for Precision EA   |
//|                                    Institutional Grade Functions |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Analyze SELL signal with multiple confirmations                  |
//+------------------------------------------------------------------+
bool AnalyzeSellSignal()
{
    int confirmations = 0;
    signal_strength = 0.0;
    
    // 1. RSI Analysis
    double rsi_current = GetRSIValue(0);
    double rsi_previous = GetRSIValue(1);
    
    if(rsi_current != EMPTY_VALUE && rsi_previous != EMPTY_VALUE)
    {
        if(rsi_previous >= RSI_Overbought && rsi_current < rsi_previous && rsi_current < RSI_Overbought)
        {
            confirmations++;
            signal_strength += 25.0;
        }
    }
    
    // 2. MACD Analysis
    double macd_main[], macd_signal[];
    ArraySetAsSeries(macd_main, true);
    ArraySetAsSeries(macd_signal, true);
    
    if(CopyBuffer(macd_handle, 0, 0, 3, macd_main) >= 3 && 
       CopyBuffer(macd_handle, 1, 0, 3, macd_signal) >= 3)
    {
        // Bearish crossover or momentum
        if((macd_main[0] < macd_signal[0] && macd_main[1] >= macd_signal[1]) ||
           (macd_main[0] < macd_signal[0] && macd_main[0] < macd_main[1]))
        {
            confirmations++;
            signal_strength += 25.0;
        }
    }
    
    // 3. EMA Trend Analysis
    double ema_fast = GetEMAValue(ema_fast_handle, 0);
    double ema_slow = GetEMAValue(ema_slow_handle, 0);
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    if(ema_fast != EMPTY_VALUE && ema_slow != EMPTY_VALUE)
    {
        if(ema_fast < ema_slow && current_price < ema_fast)
        {
            confirmations++;
            signal_strength += 25.0;
        }
    }
    
    // 4. Market Structure Analysis
    if(RequireStructureBreak && AnalyzeMarketStructure(false))
    {
        confirmations++;
        signal_strength += 25.0;
    }
    else if(!RequireStructureBreak)
    {
        confirmations++;
        signal_strength += 25.0;
    }
    
    // Require at least 3 confirmations for high-confidence signal
    bool signal_confirmed = (confirmations >= 3 && signal_strength >= 75.0);
    
    if(signal_confirmed)
    {
        Print("SELL Signal Confirmed - Confirmations: ", confirmations, ", Strength: ", signal_strength, "%");
    }
    
    return signal_confirmed;
}

//+------------------------------------------------------------------+
//| Analyze market structure for breakouts                           |
//+------------------------------------------------------------------+
bool AnalyzeMarketStructure(bool is_buy_signal)
{
    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    
    if(CopyHigh(_Symbol, PERIOD_M15, 0, StructurePeriod + 1, high) < StructurePeriod + 1 ||
       CopyLow(_Symbol, PERIOD_M15, 0, StructurePeriod + 1, low) < StructurePeriod + 1 ||
       CopyClose(_Symbol, PERIOD_M15, 0, StructurePeriod + 1, close) < StructurePeriod + 1)
        return false;
    
    // Find recent high and low
    double recent_high = high[ArrayMaximum(high, 1, StructurePeriod)];
    double recent_low = low[ArrayMinimum(low, 1, StructurePeriod)];
    
    double current_price = close[0];
    
    if(is_buy_signal)
    {
        // Check for breakout above recent high
        return (current_price > recent_high);
    }
    else
    {
        // Check for breakdown below recent low
        return (current_price < recent_low);
    }
}

//+------------------------------------------------------------------+
//| Execute BUY trade with precise parameters                        |
//+------------------------------------------------------------------+
void ExecuteBuyTrade(double price)
{
    double lot_size = CalculateLotSize();
    double sl = price - StopLossPips * _Point * 10; // Convert pips to points
    double tp = price + TakeProfitPips * _Point * 10;
    
    // Normalize prices
    sl = NormalizeDouble(sl, _Digits);
    tp = NormalizeDouble(tp, _Digits);
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lot_size;
    request.type = ORDER_TYPE_BUY;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = magic_number;
    request.comment = StringFormat("BUY-XAUUSD-S%.0f", signal_strength);
    request.deviation = 3;
    
    if(OrderSend(request, result))
    {
        trades_today++;
        buy_trade_taken = true;
        last_trade_price = price;
        total_trades_count++;
        
        Print("=== BUY TRADE EXECUTED ===");
        Print("Ticket: ", result.order);
        Print("Price: ", DoubleToString(price, _Digits));
        Print("SL: ", DoubleToString(sl, _Digits), " (", StopLossPips, " pips)");
        Print("TP: ", DoubleToString(tp, _Digits), " (", TakeProfitPips, " pips)");
        Print("Lot Size: ", DoubleToString(lot_size, 2));
        Print("Signal Strength: ", DoubleToString(signal_strength, 1), "%");
        Print("Trades Today: ", trades_today, "/", MaxTradesPerDay);
        Print("ATR: ", DoubleToString(current_atr, 4));
        Print("Session: ", is_overlap_session ? "OVERLAP" : (is_london_session ? "LONDON" : "NY"));
        Print("========================");
    }
    else
    {
        Print("ERROR: Failed to execute BUY trade. Error: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| Execute SELL trade with precise parameters                       |
//+------------------------------------------------------------------+
void ExecuteSellTrade(double price)
{
    double lot_size = CalculateLotSize();
    double sl = price + StopLossPips * _Point * 10; // Convert pips to points
    double tp = price - TakeProfitPips * _Point * 10;
    
    // Normalize prices
    sl = NormalizeDouble(sl, _Digits);
    tp = NormalizeDouble(tp, _Digits);
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lot_size;
    request.type = ORDER_TYPE_SELL;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = magic_number;
    request.comment = StringFormat("SELL-XAUUSD-S%.0f", signal_strength);
    request.deviation = 3;
    
    if(OrderSend(request, result))
    {
        trades_today++;
        sell_trade_taken = true;
        last_trade_price = price;
        total_trades_count++;
        
        Print("=== SELL TRADE EXECUTED ===");
        Print("Ticket: ", result.order);
        Print("Price: ", DoubleToString(price, _Digits));
        Print("SL: ", DoubleToString(sl, _Digits), " (", StopLossPips, " pips)");
        Print("TP: ", DoubleToString(tp, _Digits), " (", TakeProfitPips, " pips)");
        Print("Lot Size: ", DoubleToString(lot_size, 2));
        Print("Signal Strength: ", DoubleToString(signal_strength, 1), "%");
        Print("Trades Today: ", trades_today, "/", MaxTradesPerDay);
        Print("ATR: ", DoubleToString(current_atr, 4));
        Print("Session: ", is_overlap_session ? "OVERLAP" : (is_london_session ? "LONDON" : "NY"));
        Print("=========================");
    }
    else
    {
        Print("ERROR: Failed to execute SELL trade. Error: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| Calculate optimal lot size based on risk                         |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    if(LotSize > 0)
        return LotSize;
    
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = balance * RiskPercent / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double stop_loss_value = StopLossPips * _Point * 10; // Convert pips to price
    
    double lot_size = risk_amount / (stop_loss_value * tick_value / _Point);
    
    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;
    
    return lot_size;
}

//+------------------------------------------------------------------+
//| Get RSI value                                                    |
//+------------------------------------------------------------------+
double GetRSIValue(int shift)
{
    double rsi_buffer[];
    ArraySetAsSeries(rsi_buffer, true);
    
    if(CopyBuffer(rsi_handle, 0, shift, 1, rsi_buffer) < 1)
        return EMPTY_VALUE;
    
    return rsi_buffer[0];
}

//+------------------------------------------------------------------+
//| Get EMA value                                                    |
//+------------------------------------------------------------------+
double GetEMAValue(int handle, int shift)
{
    double ema_buffer[];
    ArraySetAsSeries(ema_buffer, true);
    
    if(CopyBuffer(handle, 0, shift, 1, ema_buffer) < 1)
        return EMPTY_VALUE;
    
    return ema_buffer[0];
}

//+------------------------------------------------------------------+
//| Trade result event handler                                       |
//+------------------------------------------------------------------+
void OnTrade()
{
    // Update statistics when trades close
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
               PositionGetInteger(POSITION_MAGIC) == magic_number)
            {
                double profit = PositionGetDouble(POSITION_PROFIT);
                if(profit != 0)
                {
                    total_profit += profit;
                    daily_profit += profit;
                    
                    if(profit > 0)
                        winning_trades_count++;
                    
                    double pips = profit > 0 ? TakeProfitPips : -StopLossPips;
                    daily_pips += pips;
                    
                    Print("Trade closed with ", DoubleToString(pips, 0), " pips. Daily total: ", 
                          DoubleToString(daily_pips, 0), " pips");
                          
                    // Check if daily target reached
                    if(daily_pips >= MaxTradesPerDay * TakeProfitPips)
                    {
                        Print("DAILY TARGET ACHIEVED! ", DoubleToString(daily_pips, 0), " pips earned today.");
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Generate daily performance report                                |
//+------------------------------------------------------------------+
void GenerateDailyReport()
{
    string filename = "XAUUSD_Daily_Report_" + TimeToString(TimeCurrent(), TIME_DATE) + ".txt";
    int file_handle = FileOpen(filename, FILE_WRITE | FILE_TXT);
    
    if(file_handle != INVALID_HANDLE)
    {
        double win_rate = (total_trades_count > 0) ? (double)winning_trades_count / total_trades_count * 100.0 : 0.0;
        double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
        double drawdown = (max_equity > 0) ? (max_equity - current_equity) / max_equity * 100.0 : 0.0;
        
        FileWrite(file_handle, "XAUUSD Precision Trader - Daily Report");
        FileWrite(file_handle, "Date: " + TimeToString(TimeCurrent(), TIME_DATE));
        FileWrite(file_handle, "Time: " + TimeToString(TimeCurrent(), TIME_SECONDS));
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== DAILY PERFORMANCE ===");
        FileWrite(file_handle, "Trades Today: " + IntegerToString(trades_today) + "/" + IntegerToString(MaxTradesPerDay));
        FileWrite(file_handle, "Daily Pips: " + DoubleToString(daily_pips, 0));
        FileWrite(file_handle, "Target Pips: " + IntegerToString(MaxTradesPerDay * TakeProfitPips));
        FileWrite(file_handle, "Daily Profit: $" + DoubleToString(daily_profit, 2));
        FileWrite(file_handle, "Target Achievement: " + DoubleToString(daily_pips / (MaxTradesPerDay * TakeProfitPips) * 100.0, 1) + "%");
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== OVERALL STATISTICS ===");
        FileWrite(file_handle, "Total Trades: " + IntegerToString(total_trades_count));
        FileWrite(file_handle, "Winning Trades: " + IntegerToString(winning_trades_count));
        FileWrite(file_handle, "Win Rate: " + DoubleToString(win_rate, 1) + "%");
        FileWrite(file_handle, "Total Profit: $" + DoubleToString(total_profit, 2));
        FileWrite(file_handle, "Current Drawdown: " + DoubleToString(drawdown, 2) + "%");
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== MARKET CONDITIONS ===");
        FileWrite(file_handle, "Current ATR: " + DoubleToString(current_atr, 4));
        FileWrite(file_handle, "London Session: " + (is_london_session ? "Active" : "Inactive"));
        FileWrite(file_handle, "NY Session: " + (is_newyork_session ? "Active" : "Inactive"));
        FileWrite(file_handle, "Overlap: " + (is_overlap_session ? "Active" : "Inactive"));
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== TRADE HISTORY TODAY ===");
        FileWrite(file_handle, "BUY Trade Taken: " + (buy_trade_taken ? "Yes" : "No"));
        FileWrite(file_handle, "SELL Trade Taken: " + (sell_trade_taken ? "Yes" : "No"));
        
        FileClose(file_handle);
        Print("Daily report saved to: ", filename);
    }
}
