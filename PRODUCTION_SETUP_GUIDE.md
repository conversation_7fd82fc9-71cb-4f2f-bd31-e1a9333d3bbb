# XAUUSD Precision Trader - Production Setup Guide

## 🎯 **EA Overview**

**XAUUSD_PrecisionTrader.mq5** is an institutional-grade Expert Advisor designed for professional XAUUSD trading with the following specifications:

- **Daily Target**: 300 pips (2 trades × 150 pips each)
- **Maximum Trades**: 2 per day (1 BUY, 1 SELL)
- **Signal Confirmation**: Minimum 3 technical indicators must align
- **Risk Management**: Conservative 1-2% risk per trade
- **Session Focus**: London/NY overlap for optimal volatility

## 📁 **Required Files**

```
XAUUSD-Production-EA/
├── XAUUSD_PrecisionTrader.mq5    # Main EA file
├── PrecisionTrader_Functions.mqh  # Helper functions
└── PRODUCTION_SETUP_GUIDE.md      # This guide
```

## ⚙️ **Installation Steps**

### 1. File Placement
```
Copy both files to: MetaTrader 5/MQL5/Experts/
```

### 2. Compilation
- Open MetaEditor (F4 in MT5)
- Open `XAUUSD_PrecisionTrader.mq5`
- Press F7 to compile
- Ensure zero compilation errors

### 3. Chart Setup
- Open XAUUSD M1 chart
- Drag EA from Navigator to chart
- Configure parameters (see below)
- Enable AutoTrading

## 🔧 **Optimal Parameter Settings**

### **Core Trading Settings**
```
LotSize = 0.01                    // Use auto-sizing (0) for dynamic lots
RiskPercent = 1.5                 // Conservative 1.5% risk per trade
StopLossPips = 60                 // 60 pips stop loss
TakeProfitPips = 150              // 150 pips take profit
MaxTradesPerDay = 2               // Exactly 2 trades maximum
MinPipsBetweenTrades = 50         // 50 pips minimum between entries
```

### **Signal Confirmation**
```
RSI_Period = 14                   // Standard RSI period
RSI_Oversold = 25                 // Conservative oversold level
RSI_Overbought = 75               // Conservative overbought level
MACD_Fast = 12                    // MACD fast EMA
MACD_Slow = 26                    // MACD slow EMA
MACD_Signal = 9                   // MACD signal line
EMA_Fast = 21                     // Fast EMA for trend
EMA_Slow = 50                     // Slow EMA for trend
ATR_Period = 14                   // ATR for volatility analysis
```

### **Market Structure**
```
StructurePeriod = 20              // Lookback for structure analysis
MinVolatilityATR = 0.5            // Minimum ATR to trade
MaxVolatilityATR = 3.0            // Maximum ATR to trade
RequireStructureBreak = true      // Require breakout confirmation
```

### **Time Management**
```
UseSessionFilter = true           // Enable session filtering
LondonStartHour = 8               // London session start (GMT)
LondonEndHour = 17                // London session end (GMT)
NewYorkStartHour = 13             // New York session start (GMT)
NewYorkEndHour = 22               // New York session end (GMT)
TradeOverlapOnly = true           // Trade only during overlap (13:00-17:00 GMT)
```

### **Risk Management**
```
MaxDailyLoss = 200.0              // Maximum daily loss ($200)
MaxDrawdownPercent = 15.0         // Stop trading at 15% drawdown
UseTrailingStop = true            // Enable trailing stops
TrailingStopPips = 30             // 30 pips trailing distance
BreakevenPips = 20                // Move to breakeven after 20 pips profit
```

## 🎯 **Signal Confirmation Logic**

The EA requires **minimum 3 out of 4 confirmations** before executing trades:

### **BUY Signal Confirmations**
1. **RSI**: Previous ≤ 25, Current > Previous and > 25
2. **MACD**: Bullish crossover or momentum above signal line
3. **EMA**: Fast EMA > Slow EMA AND Price > Fast EMA
4. **Structure**: Price breaks above recent 20-period high (if enabled)

### **SELL Signal Confirmations**
1. **RSI**: Previous ≥ 75, Current < Previous and < 75
2. **MACD**: Bearish crossover or momentum below signal line
3. **EMA**: Fast EMA < Slow EMA AND Price < Fast EMA
4. **Structure**: Price breaks below recent 20-period low (if enabled)

## 📊 **Expected Performance**

### **Daily Targets**
- **Trades per Day**: 2 maximum (1 BUY, 1 SELL)
- **Pips per Trade**: 150 pips target
- **Daily Target**: 300 pips total
- **Risk per Trade**: 60 pips maximum

### **Performance Metrics**
- **Expected Win Rate**: 60-70%
- **Risk:Reward Ratio**: 1:2.5 (60 pips risk : 150 pips reward)
- **Monthly Target**: 6,000-9,000 pips (20-30 trading days)
- **Maximum Drawdown**: <15%

## 🕐 **Optimal Trading Sessions**

### **Primary Session (Recommended)**
- **Time**: 13:00-17:00 GMT (London/NY Overlap)
- **Volatility**: Highest
- **Spread**: Typically lowest
- **Liquidity**: Maximum

### **Secondary Sessions**
- **London**: 08:00-17:00 GMT
- **New York**: 13:00-22:00 GMT

## 🛡️ **Risk Management Features**

### **Position Sizing**
- Dynamic lot calculation based on account balance and risk percentage
- Maximum risk per trade: 1.5% of account balance
- Automatic lot size normalization

### **Stop Loss Management**
- Fixed 60-pip stop loss on all trades
- Automatic breakeven move after 20 pips profit
- Trailing stop at 30 pips distance

### **Daily Limits**
- Maximum 2 trades per day
- Daily loss limit: $200
- Automatic trading halt if limits exceeded

### **Drawdown Protection**
- Maximum 15% account drawdown
- Automatic trading suspension if exceeded
- Real-time equity monitoring

## 📈 **Trade Execution Process**

### **Entry Process**
1. Wait for new M1 bar
2. Check session and time filters
3. Verify market conditions (ATR, spread)
4. Analyze 4 signal confirmations
5. Execute trade only if ≥3 confirmations
6. Set precise SL/TP levels

### **Trade Management**
1. Monitor for breakeven opportunity (20 pips profit)
2. Apply trailing stop if enabled
3. Track daily profit/loss
4. Update performance statistics

### **Exit Process**
1. Take profit at 150 pips
2. Stop loss at 60 pips
3. Trailing stop activation
4. End-of-day closure if needed

## 📊 **Monitoring and Reporting**

### **Real-Time Monitoring**
- Live trade status in Expert tab
- Signal strength percentage display
- Daily pip count tracking
- Session status monitoring

### **Daily Reports**
- Automatic daily performance report generation
- Trade statistics and win rate
- Market condition analysis
- Target achievement percentage

### **Log Information**
- Detailed signal analysis for each trade
- Entry/exit prices and pip counts
- Risk management actions
- Market condition data

## ⚠️ **Important Warnings**

### **Broker Requirements**
- **ECN/STP Broker**: Required for optimal execution
- **Maximum Spread**: 5 pips average for XAUUSD
- **Execution Speed**: <100ms average
- **Slippage**: Minimal during major sessions

### **Account Requirements**
- **Minimum Balance**: $1,000 recommended
- **Leverage**: 1:100 or higher
- **Currency**: USD preferred
- **Account Type**: Standard or ECN

### **Market Conditions**
- **Avoid News Events**: Major economic releases
- **Holiday Trading**: Reduced during holidays
- **Weekend Gaps**: Monitor Sunday opening gaps
- **Volatility Spikes**: EA will filter extreme conditions

## 🔧 **Troubleshooting**

### **No Trades Executing**
- Check session filters (overlap time)
- Verify ATR is within range (0.5-3.0)
- Ensure spread is <5 pips
- Confirm signal strength ≥75%

### **High Drawdown**
- Reduce RiskPercent to 1.0%
- Enable stricter filters
- Check broker execution quality
- Verify stop loss functionality

### **Compilation Errors**
- Ensure both .mq5 and .mqh files are present
- Check file paths and names
- Verify MT5 version compatibility
- Clear MetaEditor cache

## 📞 **Support and Optimization**

### **Performance Monitoring**
- Review daily reports regularly
- Track win rate and pip targets
- Monitor drawdown levels
- Adjust parameters if needed

### **Optimization Schedule**
- Weekly performance review
- Monthly parameter optimization
- Quarterly strategy assessment
- Annual system upgrade

---

**Disclaimer**: This EA is designed for professional trading. Always test thoroughly on demo accounts before live trading. Past performance does not guarantee future results. Trade responsibly and never risk more than you can afford to lose.
