//+------------------------------------------------------------------+
//|                                                    Dashboard.mqh |
//|                                    Performance Dashboard         |
//|                                             For XAUUSD Scalping |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Dashboard Class                                                  |
//+------------------------------------------------------------------+
class CDashboard
{
private:
    string            m_symbol;
    int               m_chart_id;
    
    // Dashboard objects
    string            m_panel_name;
    string            m_title_name;
    string            m_info_names[20];
    
    // Colors and styles
    color             m_bg_color;
    color             m_text_color;
    color             m_profit_color;
    color             m_loss_color;
    
    // Performance metrics
    double            m_total_profit;
    int               m_total_trades;
    int               m_winning_trades;
    double            m_max_drawdown;
    double            m_daily_profit;
    
public:
    // Constructor
    CDashboard(string symbol, int chart_id = 0);
    
    // Destructor
    ~CDashboard();
    
    // Dashboard management
    void              CreateDashboard();
    void              UpdateDashboard(CRiskManager* risk_manager, CMarketAnalysis* market_analysis);
    void              RemoveDashboard();
    
    // Performance tracking
    void              UpdatePerformanceMetrics(double profit, bool is_win);
    void              ResetDailyMetrics();
    
    // Display functions
    void              ShowTradeInfo(string trade_type, double lot_size, double price, double sl, double tp);
    void              ShowMarketConditions(bool is_trending, bool high_volatility, string session);
    void              ShowRiskMetrics(double daily_pnl, double drawdown, double win_rate);
    
private:
    // Helper functions
    void              CreatePanel();
    void              CreateTitle();
    void              CreateInfoLabels();
    void              UpdateLabel(string name, string text, color clr = clrWhite);
    string            FormatCurrency(double value);
    string            FormatPercent(double value);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CDashboard::CDashboard(string symbol, int chart_id = 0)
{
    m_symbol = symbol;
    m_chart_id = chart_id;
    m_panel_name = "EA_Panel_" + symbol;
    m_title_name = "EA_Title_" + symbol;
    
    // Initialize colors
    m_bg_color = C'25,25,25';
    m_text_color = clrWhite;
    m_profit_color = clrLimeGreen;
    m_loss_color = clrRed;
    
    // Initialize metrics
    m_total_profit = 0.0;
    m_total_trades = 0;
    m_winning_trades = 0;
    m_max_drawdown = 0.0;
    m_daily_profit = 0.0;
    
    // Initialize info label names
    for(int i = 0; i < 20; i++)
    {
        m_info_names[i] = "EA_Info_" + symbol + "_" + IntegerToString(i);
    }
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CDashboard::~CDashboard()
{
    RemoveDashboard();
}

//+------------------------------------------------------------------+
//| Create dashboard                                                 |
//+------------------------------------------------------------------+
void CDashboard::CreateDashboard()
{
    CreatePanel();
    CreateTitle();
    CreateInfoLabels();
}

//+------------------------------------------------------------------+
//| Update dashboard with current data                               |
//+------------------------------------------------------------------+
void CDashboard::UpdateDashboard(CRiskManager* risk_manager, CMarketAnalysis* market_analysis)
{
    int y_offset = 50;
    int line_height = 18;
    
    // Account information
    UpdateLabel(m_info_names[0], "Account: " + FormatCurrency(AccountInfoDouble(ACCOUNT_BALANCE)), m_text_color);
    UpdateLabel(m_info_names[1], "Equity: " + FormatCurrency(AccountInfoDouble(ACCOUNT_EQUITY)), m_text_color);
    
    // Risk management data
    if(risk_manager != NULL)
    {
        double daily_pnl = risk_manager.GetDailyProfit();
        color pnl_color = (daily_pnl >= 0) ? m_profit_color : m_loss_color;
        
        UpdateLabel(m_info_names[2], "Daily P&L: " + FormatCurrency(daily_pnl), pnl_color);
        UpdateLabel(m_info_names[3], "Win Rate: " + FormatPercent(risk_manager.GetWinRate()), m_text_color);
        UpdateLabel(m_info_names[4], "Drawdown: " + FormatPercent(risk_manager.GetCurrentDrawdown()), 
                   (risk_manager.GetCurrentDrawdown() > 10) ? m_loss_color : m_text_color);
    }
    
    // Market analysis data
    if(market_analysis != NULL)
    {
        string volatility = market_analysis.IsHighVolatility() ? "HIGH" : "NORMAL";
        string trend = market_analysis.IsTrendingMarket() ? "TRENDING" : "RANGING";
        string session = "";
        
        if(market_analysis.IsSessionOverlap())
            session = "OVERLAP";
        else if(market_analysis.IsLondonSession())
            session = "LONDON";
        else if(market_analysis.IsNewYorkSession())
            session = "NEW YORK";
        else if(market_analysis.IsAsianSession())
            session = "ASIAN";
        
        UpdateLabel(m_info_names[5], "Volatility: " + volatility, 
                   market_analysis.IsHighVolatility() ? m_profit_color : m_text_color);
        UpdateLabel(m_info_names[6], "Market: " + trend, m_text_color);
        UpdateLabel(m_info_names[7], "Session: " + session, m_text_color);
        UpdateLabel(m_info_names[8], "ATR: " + DoubleToString(market_analysis.GetATR(), 1), m_text_color);
    }
    
    // Trading statistics
    UpdateLabel(m_info_names[9], "Total Trades: " + IntegerToString(m_total_trades), m_text_color);
    UpdateLabel(m_info_names[10], "Open Positions: " + IntegerToString(PositionsTotal()), m_text_color);
    
    // Current spread
    double spread = (SymbolInfoDouble(m_symbol, SYMBOL_ASK) - SymbolInfoDouble(m_symbol, SYMBOL_BID)) / SymbolInfoDouble(m_symbol, SYMBOL_POINT);
    UpdateLabel(m_info_names[11], "Spread: " + DoubleToString(spread, 1) + " pts", m_text_color);
    
    // Last update time
    UpdateLabel(m_info_names[12], "Updated: " + TimeToString(TimeCurrent(), TIME_SECONDS), clrGray);
    
    ChartRedraw(m_chart_id);
}

//+------------------------------------------------------------------+
//| Create main panel                                                |
//+------------------------------------------------------------------+
void CDashboard::CreatePanel()
{
    ObjectDelete(m_chart_id, m_panel_name);
    
    ObjectCreate(m_chart_id, m_panel_name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(m_chart_id, m_panel_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(m_chart_id, m_panel_name, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(m_chart_id, m_panel_name, OBJPROP_YDISTANCE, 10);
    ObjectSetInteger(m_chart_id, m_panel_name, OBJPROP_XSIZE, 250);
    ObjectSetInteger(m_chart_id, m_panel_name, OBJPROP_YSIZE, 300);
    ObjectSetInteger(m_chart_id, m_panel_name, OBJPROP_BGCOLOR, m_bg_color);
    ObjectSetInteger(m_chart_id, m_panel_name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(m_chart_id, m_panel_name, OBJPROP_BORDER_COLOR, clrGray);
    ObjectSetInteger(m_chart_id, m_panel_name, OBJPROP_BACK, false);
    ObjectSetInteger(m_chart_id, m_panel_name, OBJPROP_SELECTABLE, false);
}

//+------------------------------------------------------------------+
//| Create title                                                     |
//+------------------------------------------------------------------+
void CDashboard::CreateTitle()
{
    ObjectDelete(m_chart_id, m_title_name);
    
    ObjectCreate(m_chart_id, m_title_name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(m_chart_id, m_title_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(m_chart_id, m_title_name, OBJPROP_XDISTANCE, 20);
    ObjectSetInteger(m_chart_id, m_title_name, OBJPROP_YDISTANCE, 20);
    ObjectSetString(m_chart_id, m_title_name, OBJPROP_TEXT, "XAUUSD Scalping EA");
    ObjectSetString(m_chart_id, m_title_name, OBJPROP_FONT, "Arial Bold");
    ObjectSetInteger(m_chart_id, m_title_name, OBJPROP_FONTSIZE, 12);
    ObjectSetInteger(m_chart_id, m_title_name, OBJPROP_COLOR, clrYellow);
    ObjectSetInteger(m_chart_id, m_title_name, OBJPROP_SELECTABLE, false);
}

//+------------------------------------------------------------------+
//| Create info labels                                               |
//+------------------------------------------------------------------+
void CDashboard::CreateInfoLabels()
{
    int y_start = 50;
    int line_height = 18;
    
    for(int i = 0; i < 15; i++)
    {
        ObjectDelete(m_chart_id, m_info_names[i]);
        
        ObjectCreate(m_chart_id, m_info_names[i], OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(m_chart_id, m_info_names[i], OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(m_chart_id, m_info_names[i], OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(m_chart_id, m_info_names[i], OBJPROP_YDISTANCE, y_start + i * line_height);
        ObjectSetString(m_chart_id, m_info_names[i], OBJPROP_TEXT, "");
        ObjectSetString(m_chart_id, m_info_names[i], OBJPROP_FONT, "Consolas");
        ObjectSetInteger(m_chart_id, m_info_names[i], OBJPROP_FONTSIZE, 9);
        ObjectSetInteger(m_chart_id, m_info_names[i], OBJPROP_COLOR, m_text_color);
        ObjectSetInteger(m_chart_id, m_info_names[i], OBJPROP_SELECTABLE, false);
    }
}

//+------------------------------------------------------------------+
//| Update label text and color                                      |
//+------------------------------------------------------------------+
void CDashboard::UpdateLabel(string name, string text, color clr = clrWhite)
{
    ObjectSetString(m_chart_id, name, OBJPROP_TEXT, text);
    ObjectSetInteger(m_chart_id, name, OBJPROP_COLOR, clr);
}

//+------------------------------------------------------------------+
//| Remove dashboard                                                 |
//+------------------------------------------------------------------+
void CDashboard::RemoveDashboard()
{
    ObjectDelete(m_chart_id, m_panel_name);
    ObjectDelete(m_chart_id, m_title_name);
    
    for(int i = 0; i < 20; i++)
    {
        ObjectDelete(m_chart_id, m_info_names[i]);
    }
}

//+------------------------------------------------------------------+
//| Format currency value                                            |
//+------------------------------------------------------------------+
string CDashboard::FormatCurrency(double value)
{
    return "$" + DoubleToString(value, 2);
}

//+------------------------------------------------------------------+
//| Format percentage value                                          |
//+------------------------------------------------------------------+
string CDashboard::FormatPercent(double value)
{
    return DoubleToString(value, 2) + "%";
}
