//+------------------------------------------------------------------+
//|                                   EURUSD_AggressiveRSITrader.mq5 |
//+------------------------------------------------------------------+
#property copyright "Aggressive RSI Trader"
#property version   "1.00"

//--- Trading Settings
input double   LotSize = 0.1;           // Fixed lot size
input int      StopLossPips = 50;       // Stop Loss (pips)
input int      TakeProfitPips = 100;    // Take Profit (pips)
input int      RSI_Period = 14;         // RSI period
input int      RSI_Oversold = 40;       // RSI oversold level
input int      RSI_Overbought = 60;     // RSI overbought level

//--- Global Variables
int            rsi_handle;
int            magic_number = 789456;
double         signal_strength = 0.0;
int            trades_today = 0;
bool           buy_trade_taken = false;
bool           sell_trade_taken = false;
double         daily_pips = 0.0;
double         daily_profit = 0.0;
double         total_profit = 0.0;
double         last_trade_price = 0.0;
double         max_equity = 0.0;
int            total_trades_count = 0;
int            winning_trades_count = 0;

//--- Additional Settings
input double   RiskPercent = 2.0;         // Risk per trade (%)
input int      MaxTradesPerDay = 3;       // Maximum trades per day
input double   MaxDailyLoss = 500.0;      // Maximum daily loss ($)
input double   MaxDrawdownPercent = 5.0;   // Maximum drawdown (%)
input bool     UseTrailingStop = false;    // Use trailing stop
input int      TrailingStopPips = 20;     // Trailing stop distance (pips)
input int      BreakevenPips = 30;        // Breakeven pips

//+------------------------------------------------------------------+
//| Expert initialization function                                      |
//+------------------------------------------------------------------+
int OnInit()
{
    rsi_handle = iRSI(_Symbol, PERIOD_M1, RSI_Period, PRICE_CLOSE);
    return(rsi_handle != INVALID_HANDLE ? INIT_SUCCEEDED : INIT_FAILED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    IndicatorRelease(rsi_handle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                               |
//+------------------------------------------------------------------+
void OnTick()
{
    // Manage existing positions first
    ManageExistingPositions();

    // Reset daily counters if it's a new day
    static datetime last_day = 0;
    datetime current_time = TimeCurrent();
    if(TimeDay(current_time) != TimeDay(last_day))
    {
        ResetDailyCounters();
        last_day = current_time;
    }

    // Check for new bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, PERIOD_M1, 0);
    if(current_bar_time == last_bar_time) return;
    last_bar_time = current_bar_time;

    // Check if we can take more trades today
    if(trades_today >= MaxTradesPerDay) return;

    // Check market conditions and risk management
    if(!IsMarketConditionsSuitable() || !CheckRiskManagement()) return;

    // Get RSI value
    double rsi_buffer[];
    ArraySetAsSeries(rsi_buffer, true);
    if(CopyBuffer(rsi_handle, 0, 0, 1, rsi_buffer) < 1) return;
    double rsi = rsi_buffer[0];

    Print("Current RSI value: ", DoubleToString(rsi, 1));  // Debug print

    // Execute trades based on RSI
    if(rsi <= RSI_Oversold && !buy_trade_taken) 
    {
        trades_today++;
        buy_trade_taken = true;
        ExecuteBuyTrade();
        Print("Buy signal triggered at RSI: ", DoubleToString(rsi, 1));
    }
    else if(rsi >= RSI_Overbought && !sell_trade_taken) 
    {
        trades_today++;
        sell_trade_taken = true;
        ExecuteSellTrade();
        Print("Sell signal triggered at RSI: ", DoubleToString(rsi, 1));
    }
}

//+------------------------------------------------------------------+
//| Get RSI value                                                    |
//+------------------------------------------------------------------+
double GetRSIValue(int shift)
{
    double rsi_buffer[];
    ArraySetAsSeries(rsi_buffer, true);

    if(CopyBuffer(rsi_handle, 0, shift, 1, rsi_buffer) < 1)
        return EMPTY_VALUE;

    return rsi_buffer[0];
}

//+------------------------------------------------------------------+
//| Reset daily trading counters                                     |
//+------------------------------------------------------------------+
void ResetDailyCounters()
{
    trades_today = 0;
    buy_trade_taken = false;
    sell_trade_taken = false;
    daily_pips = 0.0;
    daily_profit = 0.0;
    last_trade_price = 0;
}

//+------------------------------------------------------------------+
//| Check risk management conditions                                 |
//+------------------------------------------------------------------+
bool CheckRiskManagement()
{
    // Update max equity
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    if(current_equity > max_equity)
        max_equity = current_equity;

    // Check daily loss limit
    if(daily_profit <= -MaxDailyLoss)
    {
        Print("Daily loss limit reached: $", DoubleToString(daily_profit, 2));
        return false;
    }

    // Check maximum drawdown
    double drawdown_percent = (max_equity - current_equity) / max_equity * 100.0;
    if(drawdown_percent > MaxDrawdownPercent)
    {
        Print("Maximum drawdown exceeded: ", DoubleToString(drawdown_percent, 2), "%");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check if market conditions are suitable for trading              |
//+------------------------------------------------------------------+
bool IsMarketConditionsSuitable()
{
    // Check spread
    double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    if(spread > 30) // Maximum 3 pips spread for EURUSD
    {
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Manage existing positions with trailing stops                    |
//+------------------------------------------------------------------+
void ManageExistingPositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == magic_number)
            {
                ulong ticket = PositionGetTicket(i);

                if(UseTrailingStop)
                    ApplyTrailingStop(ticket);

                ApplyBreakeven(ticket);

                // Update daily profit
                daily_profit += PositionGetDouble(POSITION_PROFIT);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Apply trailing stop to position                                  |
//+------------------------------------------------------------------+
void ApplyTrailingStop(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return;

    double current_price;
    double current_sl = PositionGetDouble(POSITION_SL);
    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    double trailing_distance = TrailingStopPips * _Point * 10;

    if(pos_type == POSITION_TYPE_BUY)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        double new_sl = current_price - trailing_distance;

        if(new_sl > current_sl && new_sl > open_price)
        {
            ModifyPosition(ticket, NormalizeDouble(new_sl, _Digits), PositionGetDouble(POSITION_TP));
        }
    }
    else if(pos_type == POSITION_TYPE_SELL)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        double new_sl = current_price + trailing_distance;

        if((current_sl == 0 || new_sl < current_sl) && new_sl < open_price)
        {
            ModifyPosition(ticket, NormalizeDouble(new_sl, _Digits), PositionGetDouble(POSITION_TP));
        }
    }
}

//+------------------------------------------------------------------+
//| Apply breakeven to profitable positions                          |
//+------------------------------------------------------------------+
void ApplyBreakeven(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return;

    double current_price;
    double current_sl = PositionGetDouble(POSITION_SL);
    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    double breakeven_distance = BreakevenPips * _Point * 10;

    if(pos_type == POSITION_TYPE_BUY)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

        if(current_price >= open_price + breakeven_distance && current_sl < open_price)
        {
            double new_sl = open_price + 5 * _Point; // Small profit lock
            ModifyPosition(ticket, NormalizeDouble(new_sl, _Digits), PositionGetDouble(POSITION_TP));
            Print("Breakeven applied to BUY position: ", ticket);
        }
    }
    else if(pos_type == POSITION_TYPE_SELL)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

        if(current_price <= open_price - breakeven_distance && (current_sl == 0 || current_sl > open_price))
        {
            double new_sl = open_price - 5 * _Point; // Small profit lock
            ModifyPosition(ticket, NormalizeDouble(new_sl, _Digits), PositionGetDouble(POSITION_TP));
            Print("Breakeven applied to SELL position: ", ticket);
        }
    }
}

//+------------------------------------------------------------------+
//| Modify position SL/TP                                            |
//+------------------------------------------------------------------+
void ModifyPosition(ulong ticket, double sl, double tp)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_SLTP;
    request.position = ticket;
    request.sl = sl;
    request.tp = tp;

    if(!OrderSend(request, result))
    {
        Print("Error modifying position ", ticket, ": ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Main signal analysis and trade execution                         |
//+------------------------------------------------------------------+
void AnalyzeAndExecute()
{
    // Analyze BUY signals
    if(AnalyzeBuySignal())
    {
        ExecuteBuyTrade();
    }
    // Analyze SELL signals
    else if(AnalyzeSellSignal())
    {
        ExecuteSellTrade();
    }
}

//+------------------------------------------------------------------+
//| Analyze BUY signal with RSI confirmation                         |
//+------------------------------------------------------------------+
bool AnalyzeBuySignal()
{
    signal_strength = 0.0;

    // Check RSI for oversold condition
    double rsi = GetRSIValue(0);
    if(rsi <= RSI_Oversold)
    {
        signal_strength = 100.0;
        Print("BUY Signal Confirmed - RSI Oversold: ", DoubleToString(rsi,1), ", Strength: ", signal_strength, "%");
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| Analyze SELL signal with RSI confirmation                        |
//+------------------------------------------------------------------+
bool AnalyzeSellSignal()
{
    signal_strength = 0.0;

    // Check RSI for overbought condition
    double rsi = GetRSIValue(0);
    if(rsi >= RSI_Overbought)
    {
        signal_strength = 100.0;
        Print("SELL Signal Confirmed - RSI Overbought: ", DoubleToString(rsi,1), ", Strength: ", signal_strength, "%");
        return true;
    }

    return false;
}



//+------------------------------------------------------------------+
//| Execute BUY trade                                                  |
//+------------------------------------------------------------------+
void ExecuteBuyTrade()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = ask - StopLossPips * _Point * 10;
    double tp = ask + TakeProfitPips * _Point * 10;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = LotSize;
    request.type = ORDER_TYPE_BUY;
    request.price = ask;
    request.sl = NormalizeDouble(sl, _Digits);
    request.tp = NormalizeDouble(tp, _Digits);
    request.magic = magic_number;
    request.deviation = 5;

    if(OrderSend(request, result))
    {
        Print("=== BUY TRADE EXECUTED ===");
        Print("Ticket: ", result.order);
        Print("Price: ", DoubleToString(ask, _Digits));
        Print("SL: ", DoubleToString(sl, _Digits), " (", StopLossPips, " pips)");
        Print("TP: ", DoubleToString(tp, _Digits), " (", TakeProfitPips, " pips)");
        Print("Lot Size: ", DoubleToString(LotSize, 2));
        Print("Signal Strength: ", DoubleToString(signal_strength, 1), "%");
        Print("========================");
    }
    else
    {
        Print("ERROR: Failed to execute BUY trade. Error: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| Execute SELL trade                                                 |
//+------------------------------------------------------------------+
void ExecuteSellTrade()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = bid + StopLossPips * _Point * 10;
    double tp = bid - TakeProfitPips * _Point * 10;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = LotSize;
    request.type = ORDER_TYPE_SELL;
    request.price = bid;
    request.sl = NormalizeDouble(sl, _Digits);
    request.tp = NormalizeDouble(tp, _Digits);
    request.magic = magic_number;
    request.deviation = 5;

    if(OrderSend(request, result))
    {
        Print("=== SELL TRADE EXECUTED ===");
        Print("Ticket: ", result.order);
        Print("Price: ", DoubleToString(bid, _Digits));
        Print("SL: ", DoubleToString(sl, _Digits), " (", StopLossPips, " pips)");
        Print("TP: ", DoubleToString(tp, _Digits), " (", TakeProfitPips, " pips)");
        Print("Lot Size: ", DoubleToString(LotSize, 2));
        Print("Signal Strength: ", DoubleToString(signal_strength, 1), "%");
        Print("=========================");
    }
    else
    {
        Print("ERROR: Failed to execute SELL trade. Error: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| Calculate optimal lot size based on risk                         |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    if(LotSize > 0)
        return LotSize;

    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = balance * RiskPercent / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double stop_loss_value = StopLossPips * _Point * 10; // Convert pips to price

    double lot_size = risk_amount / (stop_loss_value * tick_value / _Point);

    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

    return lot_size;
}

//+------------------------------------------------------------------+
//| Trade result event handler                                       |
//+------------------------------------------------------------------+
void OnTrade()
{
    // Update statistics when trades close
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == magic_number)
            {
                double profit = PositionGetDouble(POSITION_PROFIT);
                if(profit != 0)
                {
                    total_profit += profit;
                    daily_profit += profit;

                    if(profit > 0)
                        winning_trades_count++;

                    double pips = profit > 0 ? TakeProfitPips : -StopLossPips;
                    daily_pips += pips;

                    Print("Trade closed with ", DoubleToString(pips, 0), " pips. Daily total: ",
                          DoubleToString(daily_pips, 0), " pips");

                    // Check if daily target reached
                    if(daily_pips >= MaxTradesPerDay * TakeProfitPips)
                    {
                        Print("DAILY TARGET ACHIEVED! ", DoubleToString(daily_pips, 0), " pips earned today.");
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Generate daily performance report                                |
//+------------------------------------------------------------------+
void GenerateDailyReport()
{
    string filename = "EURUSD_Daily_Report_" + TimeToString(TimeCurrent(), TIME_DATE) + ".txt";
    int file_handle = FileOpen(filename, FILE_WRITE | FILE_TXT);

    if(file_handle != INVALID_HANDLE)
    {
        double win_rate = (total_trades_count > 0) ? (double)winning_trades_count / total_trades_count * 100.0 : 0.0;
        double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
        double drawdown = (max_equity > 0) ? (max_equity - current_equity) / max_equity * 100.0 : 0.0;

        FileWrite(file_handle, "EURUSD Precision Trader - Daily Report");
        FileWrite(file_handle, "Date: " + TimeToString(TimeCurrent(), TIME_DATE));
        FileWrite(file_handle, "Time: " + TimeToString(TimeCurrent(), TIME_SECONDS));
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== DAILY PERFORMANCE ===");
        FileWrite(file_handle, "Trades Today: " + IntegerToString(trades_today) + "/" + IntegerToString(MaxTradesPerDay));
        FileWrite(file_handle, "Daily Pips: " + DoubleToString(daily_pips, 0));
        FileWrite(file_handle, "Target Pips: " + IntegerToString(MaxTradesPerDay * TakeProfitPips));
        FileWrite(file_handle, "Daily Profit: $" + DoubleToString(daily_profit, 2));
        FileWrite(file_handle, "Target Achievement: " + DoubleToString(daily_pips / (MaxTradesPerDay * TakeProfitPips) * 100.0, 1) + "%");
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== OVERALL STATISTICS ===");
        FileWrite(file_handle, "Total Trades: " + IntegerToString(total_trades_count));
        FileWrite(file_handle, "Winning Trades: " + IntegerToString(winning_trades_count));
        FileWrite(file_handle, "Win Rate: " + DoubleToString(win_rate, 1) + "%");
        FileWrite(file_handle, "Total Profit: $" + DoubleToString(total_profit, 2));
        FileWrite(file_handle, "Current Drawdown: " + DoubleToString(drawdown, 2) + "%");
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== TRADE HISTORY TODAY ===");
        FileWrite(file_handle, "BUY Trade Taken: " + (buy_trade_taken ? "Yes" : "No"));
        FileWrite(file_handle, "SELL Trade Taken: " + (sell_trade_taken ? "Yes" : "No"));

        FileClose(file_handle);
        Print("Daily report saved to: ", filename);
    }
}
