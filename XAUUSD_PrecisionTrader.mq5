//+------------------------------------------------------------------+
//|                                      XAUUSD_PrecisionTrader.mq5 |
//|                              Professional XAUUSD Trading System |
//|                                    Institutional Grade Execution |
//+------------------------------------------------------------------+
#property copyright "Market Dominant EA"
#property link      ""
#property version   "2.00"
#property description "Production XAUUSD Trading EA - 2 Trades/Day - 300 Pips Target"

#include "PrecisionTrader_Functions.mqh"

//--- Trading Parameters
input group "=== CORE TRADING SETTINGS ==="
input double   LotSize = 0.01;                      // Fixed lot size (0 = auto-sizing)
input double   RiskPercent = 1.5;                   // Risk per trade (%)
input int      StopLossPips = 60;                   // Stop Loss (pips)
input int      TakeProfitPips = 150;                // Take Profit (pips)
input int      MaxTradesPerDay = 2;                 // Maximum trades per day
input int      MinPipsBetweenTrades = 50;           // Minimum pips between trades

//--- Signal Confirmation Settings
input group "=== SIGNAL CONFIRMATION ==="
input int      RSI_Period = 14;                     // RSI period
input int      RSI_Oversold = 25;                   // RSI oversold level
input int      RSI_Overbought = 75;                 // RSI overbought level
input int      MACD_Fast = 12;                      // MACD fast EMA
input int      MACD_Slow = 26;                      // MACD slow EMA
input int      MACD_Signal = 9;                     // MACD signal line
input int      EMA_Fast = 21;                       // Fast EMA period
input int      EMA_Slow = 50;                       // Slow EMA period
input int      ATR_Period = 14;                     // ATR period for volatility

//--- Market Structure Settings
input group "=== MARKET STRUCTURE ==="
input int      StructurePeriod = 20;                // Structure analysis period
input double   MinVolatilityATR = 0.5;              // Minimum ATR for trading
input double   MaxVolatilityATR = 3.0;              // Maximum ATR for trading
input bool     RequireStructureBreak = true;        // Require structure break for entry

//--- Session and Time Filters
input group "=== TIME MANAGEMENT ==="
input bool     UseSessionFilter = true;             // Enable session filtering
input int      LondonStartHour = 8;                 // London session start (GMT)
input int      LondonEndHour = 17;                  // London session end (GMT)
input int      NewYorkStartHour = 13;               // New York session start (GMT)
input int      NewYorkEndHour = 22;                 // New York session end (GMT)
input bool     TradeOverlapOnly = true;             // Trade only during session overlap

//--- Advanced Risk Management
input group "=== RISK MANAGEMENT ==="
input double   MaxDailyLoss = 200.0;                // Maximum daily loss ($)
input double   MaxDrawdownPercent = 15.0;           // Maximum drawdown (%)
input bool     UseTrailingStop = true;              // Enable trailing stop
input int      TrailingStopPips = 30;               // Trailing stop distance (pips)
input int      BreakevenPips = 20;                  // Move to breakeven after X pips

//--- Global Variables
int            rsi_handle;
int            macd_handle;
int            ema_fast_handle;
int            ema_slow_handle;
int            atr_handle;
int            magic_number = 789456;

// Daily trade tracking
int            trades_today = 0;
datetime       last_trade_day = 0;
double         last_trade_price = 0;
bool           buy_trade_taken = false;
bool           sell_trade_taken = false;

// Signal confirmation variables
double         signal_strength = 0.0;
bool           structure_break_detected = false;
datetime       last_signal_time = 0;

// Performance tracking
double         daily_pips = 0.0;
double         daily_profit = 0.0;
int            total_trades_count = 0;
int            winning_trades_count = 0;
double         total_profit = 0.0;
double         max_equity = 0.0;

// Market analysis
double         current_atr = 0.0;
bool           is_london_session = false;
bool           is_newyork_session = false;
bool           is_overlap_session = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize indicators
    rsi_handle = iRSI(_Symbol, PERIOD_M1, RSI_Period, PRICE_CLOSE);
    macd_handle = iMACD(_Symbol, PERIOD_M1, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);
    ema_fast_handle = iMA(_Symbol, PERIOD_M5, EMA_Fast, 0, MODE_EMA, PRICE_CLOSE);
    ema_slow_handle = iMA(_Symbol, PERIOD_M5, EMA_Slow, 0, MODE_EMA, PRICE_CLOSE);
    atr_handle = iATR(_Symbol, PERIOD_M15, ATR_Period);

    // Validate indicator handles
    if(rsi_handle == INVALID_HANDLE || macd_handle == INVALID_HANDLE ||
       ema_fast_handle == INVALID_HANDLE || ema_slow_handle == INVALID_HANDLE ||
       atr_handle == INVALID_HANDLE)
    {
        Print("ERROR: Failed to initialize indicators");
        return INIT_FAILED;
    }

    // Validate symbol
    if(_Symbol != "XAUUSD")
    {
        Print("WARNING: EA optimized for XAUUSD. Current symbol: ", _Symbol);
    }

    // Initialize tracking variables
    ResetDailyCounters();
    max_equity = AccountInfoDouble(ACCOUNT_EQUITY);

    Print("XAUUSD Precision Trader initialized successfully");
    Print("Target: ", MaxTradesPerDay, " trades/day, ", TakeProfitPips, " pips per trade");
    Print("Daily target: ", MaxTradesPerDay * TakeProfitPips, " pips");

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    IndicatorRelease(rsi_handle);
    IndicatorRelease(macd_handle);
    IndicatorRelease(ema_fast_handle);
    IndicatorRelease(ema_slow_handle);
    IndicatorRelease(atr_handle);

    // Generate final report
    GenerateDailyReport();

    // Print final statistics
    double win_rate = (total_trades_count > 0) ? (double)winning_trades_count / total_trades_count * 100.0 : 0.0;
    Print("=== FINAL STATISTICS ===");
    Print("Total Trades: ", total_trades_count);
    Print("Winning Trades: ", winning_trades_count);
    Print("Win Rate: ", DoubleToString(win_rate, 1), "%");
    Print("Total Profit: $", DoubleToString(total_profit, 2));
    Print("Daily Pips: ", DoubleToString(daily_pips, 0));

    Print("XAUUSD Precision Trader deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check for new bar on M1
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, PERIOD_M1, 0);

    if(current_bar_time == last_bar_time)
        return;
    last_bar_time = current_bar_time;

    // Reset daily counters if new day
    CheckNewDay();

    // Update market analysis
    UpdateMarketAnalysis();

    // Check risk management
    if(!CheckRiskManagement())
        return;

    // Check if maximum trades reached
    if(trades_today >= MaxTradesPerDay)
    {
        return; // Stop trading for the day
    }

    // Check session filters
    if(!IsValidTradingSession())
        return;

    // Check market conditions
    if(!IsMarketConditionsSuitable())
        return;

    // Manage existing positions
    ManageExistingPositions();

    // Analyze signals and execute trades
    AnalyzeAndExecute();
}

//+------------------------------------------------------------------+
//| Check for new trading day and reset counters                     |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    datetime current_day = (datetime)(TimeCurrent() / 86400) * 86400;

    if(current_day != last_trade_day)
    {
        // Generate report for previous day
        if(last_trade_day > 0)
            GenerateDailyReport();

        ResetDailyCounters();
        last_trade_day = current_day;
        Print("New trading day started. Daily counters reset.");
        Print("Target for today: ", MaxTradesPerDay * TakeProfitPips, " pips");
    }
}

//+------------------------------------------------------------------+
//| Reset daily trading counters                                     |
//+------------------------------------------------------------------+
void ResetDailyCounters()
{
    trades_today = 0;
    buy_trade_taken = false;
    sell_trade_taken = false;
    daily_pips = 0.0;
    daily_profit = 0.0;
    last_trade_price = 0;
}

//+------------------------------------------------------------------+
//| Update market analysis variables                                 |
//+------------------------------------------------------------------+
void UpdateMarketAnalysis()
{
    // Get ATR value
    double atr_buffer[];
    ArraySetAsSeries(atr_buffer, true);
    if(CopyBuffer(atr_handle, 0, 0, 1, atr_buffer) > 0)
        current_atr = atr_buffer[0];

    // Update session status
    MqlDateTime dt;
    TimeToStruct(TimeGMT(), dt);

    is_london_session = (dt.hour >= LondonStartHour && dt.hour < LondonEndHour);
    is_newyork_session = (dt.hour >= NewYorkStartHour && dt.hour < NewYorkEndHour);
    is_overlap_session = (is_london_session && is_newyork_session);
}

//+------------------------------------------------------------------+
//| Check risk management conditions                                 |
//+------------------------------------------------------------------+
bool CheckRiskManagement()
{
    // Update max equity
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    if(current_equity > max_equity)
        max_equity = current_equity;

    // Check daily loss limit
    if(daily_profit <= -MaxDailyLoss)
    {
        Print("Daily loss limit reached: $", DoubleToString(daily_profit, 2));
        return false;
    }

    // Check maximum drawdown
    double drawdown_percent = (max_equity - current_equity) / max_equity * 100.0;
    if(drawdown_percent > MaxDrawdownPercent)
    {
        Print("Maximum drawdown exceeded: ", DoubleToString(drawdown_percent, 2), "%");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check if current session is valid for trading                    |
//+------------------------------------------------------------------+
bool IsValidTradingSession()
{
    if(!UseSessionFilter)
        return true;

    if(TradeOverlapOnly)
        return is_overlap_session;

    return (is_london_session || is_newyork_session);
}

//+------------------------------------------------------------------+
//| Check if market conditions are suitable for trading              |
//+------------------------------------------------------------------+
bool IsMarketConditionsSuitable()
{
    // Check ATR volatility range
    if(current_atr < MinVolatilityATR || current_atr > MaxVolatilityATR)
    {
        return false;
    }

    // Check spread
    double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    if(spread > 50) // Maximum 5 pips spread for XAUUSD
    {
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Manage existing positions with trailing stops                    |
//+------------------------------------------------------------------+
void ManageExistingPositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == magic_number)
            {
                ulong ticket = PositionGetTicket(i);

                if(UseTrailingStop)
                    ApplyTrailingStop(ticket);

                ApplyBreakeven(ticket);

                // Update daily profit
                daily_profit += PositionGetDouble(POSITION_PROFIT);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Apply trailing stop to position                                  |
//+------------------------------------------------------------------+
void ApplyTrailingStop(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return;

    double current_price;
    double current_sl = PositionGetDouble(POSITION_SL);
    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    double trailing_distance = TrailingStopPips * _Point * 10;

    if(pos_type == POSITION_TYPE_BUY)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        double new_sl = current_price - trailing_distance;

        if(new_sl > current_sl && new_sl > open_price)
        {
            ModifyPosition(ticket, NormalizeDouble(new_sl, _Digits), PositionGetDouble(POSITION_TP));
        }
    }
    else if(pos_type == POSITION_TYPE_SELL)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        double new_sl = current_price + trailing_distance;

        if((current_sl == 0 || new_sl < current_sl) && new_sl < open_price)
        {
            ModifyPosition(ticket, NormalizeDouble(new_sl, _Digits), PositionGetDouble(POSITION_TP));
        }
    }
}

//+------------------------------------------------------------------+
//| Apply breakeven to profitable positions                          |
//+------------------------------------------------------------------+
void ApplyBreakeven(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return;

    double current_price;
    double current_sl = PositionGetDouble(POSITION_SL);
    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    double breakeven_distance = BreakevenPips * _Point * 10;

    if(pos_type == POSITION_TYPE_BUY)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

        if(current_price >= open_price + breakeven_distance && current_sl < open_price)
        {
            double new_sl = open_price + 5 * _Point; // Small profit lock
            ModifyPosition(ticket, NormalizeDouble(new_sl, _Digits), PositionGetDouble(POSITION_TP));
            Print("Breakeven applied to BUY position: ", ticket);
        }
    }
    else if(pos_type == POSITION_TYPE_SELL)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

        if(current_price <= open_price - breakeven_distance && (current_sl == 0 || current_sl > open_price))
        {
            double new_sl = open_price - 5 * _Point; // Small profit lock
            ModifyPosition(ticket, NormalizeDouble(new_sl, _Digits), PositionGetDouble(POSITION_TP));
            Print("Breakeven applied to SELL position: ", ticket);
        }
    }
}

//+------------------------------------------------------------------+
//| Modify position SL/TP                                            |
//+------------------------------------------------------------------+
void ModifyPosition(ulong ticket, double sl, double tp)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_SLTP;
    request.position = ticket;
    request.sl = sl;
    request.tp = tp;

    if(!OrderSend(request, result))
    {
        Print("Error modifying position ", ticket, ": ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Main signal analysis and trade execution                         |
//+------------------------------------------------------------------+
void AnalyzeAndExecute()
{
    // Get current price
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Check minimum distance from last trade
    if(last_trade_price > 0)
    {
        double distance_pips = MathAbs(ask - last_trade_price) / _Point / 10; // Convert to pips
        if(distance_pips < MinPipsBetweenTrades)
            return;
    }

    // Analyze BUY signals
    if(!buy_trade_taken && AnalyzeBuySignal())
    {
        ExecuteBuyTrade(ask);
    }
    // Analyze SELL signals
    else if(!sell_trade_taken && AnalyzeSellSignal())
    {
        ExecuteSellTrade(bid);
    }
}

//+------------------------------------------------------------------+
//| Analyze BUY signal with multiple confirmations                   |
//+------------------------------------------------------------------+
bool AnalyzeBuySignal()
{
    int confirmations = 0;
    signal_strength = 0.0;

    // 1. RSI Analysis
    double rsi_current = GetRSIValue(0);
    double rsi_previous = GetRSIValue(1);

    if(rsi_current != EMPTY_VALUE && rsi_previous != EMPTY_VALUE)
    {
        if(rsi_previous <= RSI_Oversold && rsi_current > rsi_previous && rsi_current > RSI_Oversold)
        {
            confirmations++;
            signal_strength += 25.0;
        }
    }

    // 2. MACD Analysis
    double macd_main[], macd_signal[];
    ArraySetAsSeries(macd_main, true);
    ArraySetAsSeries(macd_signal, true);

    if(CopyBuffer(macd_handle, 0, 0, 3, macd_main) >= 3 &&
       CopyBuffer(macd_handle, 1, 0, 3, macd_signal) >= 3)
    {
        // Bullish crossover or momentum
        if((macd_main[0] > macd_signal[0] && macd_main[1] <= macd_signal[1]) ||
           (macd_main[0] > macd_signal[0] && macd_main[0] > macd_main[1]))
        {
            confirmations++;
            signal_strength += 25.0;
        }
    }

    // 3. EMA Trend Analysis
    double ema_fast = GetEMAValue(ema_fast_handle, 0);
    double ema_slow = GetEMAValue(ema_slow_handle, 0);
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    if(ema_fast != EMPTY_VALUE && ema_slow != EMPTY_VALUE)
    {
        if(ema_fast > ema_slow && current_price > ema_fast)
        {
            confirmations++;
            signal_strength += 25.0;
        }
    }

    // 4. Market Structure Analysis
    if(RequireStructureBreak && AnalyzeMarketStructure(true))
    {
        confirmations++;
        signal_strength += 25.0;
    }
    else if(!RequireStructureBreak)
    {
        confirmations++;
        signal_strength += 25.0;
    }

    // Require at least 3 confirmations for high-confidence signal
    bool signal_confirmed = (confirmations >= 3 && signal_strength >= 75.0);

    if(signal_confirmed)
    {
        Print("BUY Signal Confirmed - Confirmations: ", confirmations, ", Strength: ", signal_strength, "%");
    }

    return signal_confirmed;
}
