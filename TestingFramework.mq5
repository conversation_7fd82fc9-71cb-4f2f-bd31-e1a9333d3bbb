//+------------------------------------------------------------------+
//|                                           TestingFramework.mq5 |
//|                                    EA Testing and Optimization  |
//|                                             For XAUUSD Scalping |
//+------------------------------------------------------------------+
#property copyright "Market Dominant EA"
#property link      ""
#property version   "1.00"
#property description "Testing Framework for XAUUSD Scalping EA"
#property script_show_inputs

//--- Input parameters for testing
input group "=== TESTING PARAMETERS ==="
input datetime TestStartDate = D'2024.01.01';      // Test start date
input datetime TestEndDate = D'2024.12.31';        // Test end date
input bool     EnableOptimization = false;          // Enable parameter optimization
input bool     GenerateReport = true;               // Generate detailed report
input bool     TestMultipleTimeframes = true;       // Test on multiple timeframes

input group "=== OPTIMIZATION RANGES ==="
input int      RSI_Period_Start = 10;               // RSI period start
input int      RSI_Period_End = 20;                 // RSI period end
input int      RSI_Period_Step = 2;                 // RSI period step
input int      StopLoss_Start = 30;                 // Stop loss start
input int      StopLoss_End = 80;                   // Stop loss end
input int      StopLoss_Step = 10;                  // Stop loss step
input int      TakeProfit_Start = 80;               // Take profit start
input int      TakeProfit_End = 200;                // Take profit end
input int      TakeProfit_Step = 20;                // Take profit step

//--- Global variables
struct TestResult
{
    double profit;
    double max_drawdown;
    int total_trades;
    int winning_trades;
    double win_rate;
    double profit_factor;
    double sharpe_ratio;
    double recovery_factor;
};

TestResult best_result;
string test_report = "";

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("Starting XAUUSD Scalping EA Testing Framework");
    
    if(EnableOptimization)
    {
        RunOptimization();
    }
    else
    {
        RunSingleTest();
    }
    
    if(GenerateReport)
    {
        GenerateTestReport();
    }
    
    Print("Testing completed. Check the report for results.");
}

//+------------------------------------------------------------------+
//| Run single test with current parameters                          |
//+------------------------------------------------------------------+
void RunSingleTest()
{
    Print("Running single test from ", TestStartDate, " to ", TestEndDate);
    
    // Initialize test environment
    TestResult result = {};
    
    // Run backtest simulation
    if(TestMultipleTimeframes)
    {
        TestResult result_m1 = RunBacktest(PERIOD_M1);
        TestResult result_m5 = RunBacktest(PERIOD_M5);
        
        // Combine results (weighted average)
        result.profit = (result_m1.profit * 0.7 + result_m5.profit * 0.3);
        result.max_drawdown = MathMax(result_m1.max_drawdown, result_m5.max_drawdown);
        result.total_trades = result_m1.total_trades + result_m5.total_trades;
        result.winning_trades = result_m1.winning_trades + result_m5.winning_trades;
        
        if(result.total_trades > 0)
            result.win_rate = (double)result.winning_trades / result.total_trades * 100.0;
    }
    else
    {
        result = RunBacktest(PERIOD_M1);
    }
    
    // Display results
    DisplayTestResults(result, "Single Test");
}

//+------------------------------------------------------------------+
//| Run optimization across parameter ranges                         |
//+------------------------------------------------------------------+
void RunOptimization()
{
    Print("Running optimization across parameter ranges");
    
    best_result.profit = -999999;
    int test_count = 0;
    
    // Nested loops for parameter optimization
    for(int rsi_period = RSI_Period_Start; rsi_period <= RSI_Period_End; rsi_period += RSI_Period_Step)
    {
        for(int sl = StopLoss_Start; sl <= StopLoss_End; sl += StopLoss_Step)
        {
            for(int tp = TakeProfit_Start; tp <= TakeProfit_End; tp += TakeProfit_Step)
            {
                test_count++;
                Print("Running test ", test_count, " - RSI:", rsi_period, " SL:", sl, " TP:", tp);
                
                TestResult result = RunParameterTest(rsi_period, sl, tp);
                
                // Check if this is the best result
                if(result.profit > best_result.profit && result.max_drawdown < 20.0)
                {
                    best_result = result;
                    test_report += StringFormat("New best result found - Test %d: Profit=%.2f, DD=%.2f%%, WR=%.1f%%\n",
                                              test_count, result.profit, result.max_drawdown, result.win_rate);
                }
            }
        }
    }
    
    DisplayTestResults(best_result, "Optimization Best Result");
}

//+------------------------------------------------------------------+
//| Run backtest for specific timeframe                              |
//+------------------------------------------------------------------+
TestResult RunBacktest(ENUM_TIMEFRAMES timeframe)
{
    TestResult result = {};
    
    // Simulate trading logic
    double balance = 10000.0;
    double equity = balance;
    double max_equity = balance;
    int trades = 0;
    int wins = 0;
    double total_profit = 0.0;
    double max_dd = 0.0;
    
    // Get historical data
    datetime start_time = TestStartDate;
    datetime end_time = TestEndDate;
    
    // Simulate trades (simplified for demonstration)
    for(datetime current_time = start_time; current_time < end_time; current_time += PeriodSeconds(timeframe))
    {
        // Simulate market conditions and signals
        if(ShouldOpenTrade(current_time, timeframe))
        {
            trades++;
            double trade_result = SimulateTrade(current_time, timeframe);
            
            if(trade_result > 0)
                wins++;
            
            total_profit += trade_result;
            equity += trade_result;
            
            if(equity > max_equity)
                max_equity = equity;
            
            double current_dd = (max_equity - equity) / max_equity * 100.0;
            if(current_dd > max_dd)
                max_dd = current_dd;
        }
    }
    
    // Calculate final metrics
    result.profit = total_profit;
    result.max_drawdown = max_dd;
    result.total_trades = trades;
    result.winning_trades = wins;
    result.win_rate = (trades > 0) ? (double)wins / trades * 100.0 : 0.0;
    result.profit_factor = CalculateProfitFactor(total_profit, trades, wins);
    result.sharpe_ratio = CalculateSharpeRatio(total_profit, max_dd);
    result.recovery_factor = (max_dd > 0) ? total_profit / max_dd : 0.0;
    
    return result;
}

//+------------------------------------------------------------------+
//| Run test with specific parameters                                |
//+------------------------------------------------------------------+
TestResult RunParameterTest(int rsi_period, int stop_loss, int take_profit)
{
    // This would normally run the EA with specific parameters
    // For demonstration, we'll simulate results
    TestResult result = {};
    
    // Simulate parameter-dependent results
    double base_profit = MathRand() % 1000 - 500; // Random profit between -500 and 500
    double rsi_factor = (rsi_period - 10) * 10.0; // RSI impact
    double sl_factor = (80 - stop_loss) * 2.0;    // SL impact
    double tp_factor = (take_profit - 100) * 1.5; // TP impact
    
    result.profit = base_profit + rsi_factor + sl_factor + tp_factor;
    result.max_drawdown = MathAbs(base_profit) / 50.0; // Simulated drawdown
    result.total_trades = 50 + MathRand() % 100;
    result.winning_trades = (int)(result.total_trades * (0.4 + MathRand() % 40 / 100.0));
    result.win_rate = (double)result.winning_trades / result.total_trades * 100.0;
    result.profit_factor = CalculateProfitFactor(result.profit, result.total_trades, result.winning_trades);
    
    return result;
}

//+------------------------------------------------------------------+
//| Check if should open trade (simplified simulation)               |
//+------------------------------------------------------------------+
bool ShouldOpenTrade(datetime time, ENUM_TIMEFRAMES timeframe)
{
    // Simplified signal generation for testing
    MqlDateTime dt;
    TimeToStruct(time, dt);
    
    // Only trade during certain hours
    if(dt.hour < 8 || dt.hour > 17)
        return false;
    
    // Random signal generation (replace with actual logic)
    return (MathRand() % 100 < 15); // 15% chance of signal
}

//+------------------------------------------------------------------+
//| Simulate trade execution and result                              |
//+------------------------------------------------------------------+
double SimulateTrade(datetime time, ENUM_TIMEFRAMES timeframe)
{
    // Simulate trade outcome
    double win_probability = 0.55; // 55% win rate
    double avg_win = 150.0;
    double avg_loss = -80.0;
    
    if(MathRand() / 32767.0 < win_probability)
        return avg_win + (MathRand() % 100 - 50); // Winning trade
    else
        return avg_loss - (MathRand() % 50);      // Losing trade
}

//+------------------------------------------------------------------+
//| Calculate profit factor                                          |
//+------------------------------------------------------------------+
double CalculateProfitFactor(double total_profit, int total_trades, int winning_trades)
{
    if(total_trades == 0 || winning_trades == total_trades)
        return 0.0;
    
    int losing_trades = total_trades - winning_trades;
    double gross_profit = total_profit > 0 ? total_profit * 1.5 : 100.0;
    double gross_loss = total_profit < 0 ? MathAbs(total_profit) * 0.5 : 50.0;
    
    return (gross_loss > 0) ? gross_profit / gross_loss : 0.0;
}

//+------------------------------------------------------------------+
//| Calculate Sharpe ratio                                           |
//+------------------------------------------------------------------+
double CalculateSharpeRatio(double total_profit, double max_drawdown)
{
    if(max_drawdown == 0)
        return 0.0;
    
    return total_profit / max_drawdown;
}

//+------------------------------------------------------------------+
//| Display test results                                             |
//+------------------------------------------------------------------+
void DisplayTestResults(TestResult &result, string test_name)
{
    Print("=== ", test_name, " Results ===");
    Print("Total Profit: $", DoubleToString(result.profit, 2));
    Print("Max Drawdown: ", DoubleToString(result.max_drawdown, 2), "%");
    Print("Total Trades: ", result.total_trades);
    Print("Winning Trades: ", result.winning_trades);
    Print("Win Rate: ", DoubleToString(result.win_rate, 1), "%");
    Print("Profit Factor: ", DoubleToString(result.profit_factor, 2));
    Print("Sharpe Ratio: ", DoubleToString(result.sharpe_ratio, 2));
    Print("Recovery Factor: ", DoubleToString(result.recovery_factor, 2));
    Print("================================");
}

//+------------------------------------------------------------------+
//| Generate comprehensive test report                               |
//+------------------------------------------------------------------+
void GenerateTestReport()
{
    string filename = "XAUUSD_EA_Test_Report_" + TimeToString(TimeCurrent(), TIME_DATE) + ".txt";
    int file_handle = FileOpen(filename, FILE_WRITE | FILE_TXT);
    
    if(file_handle != INVALID_HANDLE)
    {
        FileWrite(file_handle, "XAUUSD Scalping EA - Test Report");
        FileWrite(file_handle, "Generated: " + TimeToString(TimeCurrent()));
        FileWrite(file_handle, "Test Period: " + TimeToString(TestStartDate) + " to " + TimeToString(TestEndDate));
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== BEST RESULTS ===");
        FileWrite(file_handle, "Total Profit: $" + DoubleToString(best_result.profit, 2));
        FileWrite(file_handle, "Max Drawdown: " + DoubleToString(best_result.max_drawdown, 2) + "%");
        FileWrite(file_handle, "Win Rate: " + DoubleToString(best_result.win_rate, 1) + "%");
        FileWrite(file_handle, "Profit Factor: " + DoubleToString(best_result.profit_factor, 2));
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== OPTIMIZATION LOG ===");
        FileWrite(file_handle, test_report);
        
        FileClose(file_handle);
        Print("Test report saved to: ", filename);
    }
    else
    {
        Print("Error creating test report file");
    }
}
