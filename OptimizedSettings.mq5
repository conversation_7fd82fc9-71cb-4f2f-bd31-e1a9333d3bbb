//+------------------------------------------------------------------+
//|                                           OptimizedSettings.mq5 |
//|                                    Optimized EA Configuration   |
//|                                             For XAUUSD Scalping |
//+------------------------------------------------------------------+
#property copyright "Market Dominant EA"
#property link      ""
#property version   "1.00"
#property description "Pre-optimized settings for XAUUSD Scalping EA"
#property script_show_inputs

//--- Optimized parameter sets
input group "=== OPTIMIZATION PRESETS ==="
input ENUM_OPTIMIZATION_PRESET OptimizationPreset = CONSERVATIVE;  // Choose optimization preset

enum ENUM_OPTIMIZATION_PRESET
{
    CONSERVATIVE,    // Conservative settings (lower risk)
    BALANCED,        // Balanced settings (medium risk)
    AGGRESSIVE,      // Aggressive settings (higher risk)
    CUSTOM          // Use custom settings below
};

input group "=== CUSTOM SETTINGS (if CUSTOM selected) ==="
input double   CustomLotSize = 0.01;
input double   CustomRiskPercent = 2.0;
input int      CustomStopLoss = 50;
input int      CustomTakeProfit = 150;
input int      CustomRSI_Period = 14;

//--- Preset configurations
struct OptimizationConfig
{
    double lot_size;
    double risk_percent;
    int stop_loss;
    int take_profit;
    int rsi_period;
    int rsi_oversold;
    int rsi_overbought;
    int macd_fast;
    int macd_slow;
    int macd_signal;
    double max_daily_loss;
    int max_positions;
    bool use_trailing_stop;
    int trailing_stop;
    int trailing_step;
    double max_spread;
    int start_hour;
    int end_hour;
    bool trade_friday;
};

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("XAUUSD Scalping EA - Optimization Settings Generator");
    
    OptimizationConfig config = GetOptimizedConfig(OptimizationPreset);
    
    Print("=== SELECTED CONFIGURATION ===");
    PrintConfiguration(config);
    
    string filename = GenerateConfigFile(config);
    Print("Configuration saved to: ", filename);
    
    Print("=== SETUP INSTRUCTIONS ===");
    PrintSetupInstructions();
}

//+------------------------------------------------------------------+
//| Get optimized configuration based on preset                      |
//+------------------------------------------------------------------+
OptimizationConfig GetOptimizedConfig(ENUM_OPTIMIZATION_PRESET preset)
{
    OptimizationConfig config = {};
    
    switch(preset)
    {
        case CONSERVATIVE:
            config.lot_size = 0.01;
            config.risk_percent = 1.5;
            config.stop_loss = 60;
            config.take_profit = 120;
            config.rsi_period = 16;
            config.rsi_oversold = 25;
            config.rsi_overbought = 75;
            config.macd_fast = 12;
            config.macd_slow = 26;
            config.macd_signal = 9;
            config.max_daily_loss = 50.0;
            config.max_positions = 1;
            config.use_trailing_stop = true;
            config.trailing_stop = 40;
            config.trailing_step = 15;
            config.max_spread = 30;
            config.start_hour = 9;
            config.end_hour = 16;
            config.trade_friday = false;
            break;
            
        case BALANCED:
            config.lot_size = 0.01;
            config.risk_percent = 2.0;
            config.stop_loss = 50;
            config.take_profit = 150;
            config.rsi_period = 14;
            config.rsi_oversold = 30;
            config.rsi_overbought = 70;
            config.macd_fast = 12;
            config.macd_slow = 26;
            config.macd_signal = 9;
            config.max_daily_loss = 100.0;
            config.max_positions = 2;
            config.use_trailing_stop = true;
            config.trailing_stop = 30;
            config.trailing_step = 10;
            config.max_spread = 40;
            config.start_hour = 8;
            config.end_hour = 17;
            config.trade_friday = false;
            break;
            
        case AGGRESSIVE:
            config.lot_size = 0.02;
            config.risk_percent = 3.0;
            config.stop_loss = 40;
            config.take_profit = 180;
            config.rsi_period = 12;
            config.rsi_oversold = 35;
            config.rsi_overbought = 65;
            config.macd_fast = 10;
            config.macd_slow = 24;
            config.macd_signal = 8;
            config.max_daily_loss = 200.0;
            config.max_positions = 3;
            config.use_trailing_stop = true;
            config.trailing_stop = 25;
            config.trailing_step = 8;
            config.max_spread = 50;
            config.start_hour = 7;
            config.end_hour = 18;
            config.trade_friday = true;
            break;
            
        case CUSTOM:
            config.lot_size = CustomLotSize;
            config.risk_percent = CustomRiskPercent;
            config.stop_loss = CustomStopLoss;
            config.take_profit = CustomTakeProfit;
            config.rsi_period = CustomRSI_Period;
            config.rsi_oversold = 30;
            config.rsi_overbought = 70;
            config.macd_fast = 12;
            config.macd_slow = 26;
            config.macd_signal = 9;
            config.max_daily_loss = 100.0;
            config.max_positions = 1;
            config.use_trailing_stop = true;
            config.trailing_stop = 30;
            config.trailing_step = 10;
            config.max_spread = 40;
            config.start_hour = 8;
            config.end_hour = 17;
            config.trade_friday = false;
            break;
    }
    
    return config;
}

//+------------------------------------------------------------------+
//| Print configuration details                                      |
//+------------------------------------------------------------------+
void PrintConfiguration(OptimizationConfig &config)
{
    Print("Lot Size: ", config.lot_size);
    Print("Risk Percent: ", config.risk_percent, "%");
    Print("Stop Loss: ", config.stop_loss, " points");
    Print("Take Profit: ", config.take_profit, " points");
    Print("RSI Period: ", config.rsi_period);
    Print("RSI Levels: ", config.rsi_oversold, "/", config.rsi_overbought);
    Print("MACD: ", config.macd_fast, "/", config.macd_slow, "/", config.macd_signal);
    Print("Max Daily Loss: $", config.max_daily_loss);
    Print("Max Positions: ", config.max_positions);
    Print("Trailing Stop: ", config.use_trailing_stop ? "Enabled" : "Disabled");
    if(config.use_trailing_stop)
        Print("Trailing: ", config.trailing_stop, " points, Step: ", config.trailing_step);
    Print("Max Spread: ", config.max_spread, " points");
    Print("Trading Hours: ", config.start_hour, ":00 - ", config.end_hour, ":00 GMT");
    Print("Trade Friday: ", config.trade_friday ? "Yes" : "No");
}

//+------------------------------------------------------------------+
//| Generate configuration file                                      |
//+------------------------------------------------------------------+
string GenerateConfigFile(OptimizationConfig &config)
{
    string preset_name = "";
    switch(OptimizationPreset)
    {
        case CONSERVATIVE: preset_name = "Conservative"; break;
        case BALANCED: preset_name = "Balanced"; break;
        case AGGRESSIVE: preset_name = "Aggressive"; break;
        case CUSTOM: preset_name = "Custom"; break;
    }
    
    string filename = "XAUUSD_EA_Config_" + preset_name + "_" + TimeToString(TimeCurrent(), TIME_DATE) + ".txt";
    int file_handle = FileOpen(filename, FILE_WRITE | FILE_TXT);
    
    if(file_handle != INVALID_HANDLE)
    {
        FileWrite(file_handle, "XAUUSD Scalping EA - Optimized Configuration");
        FileWrite(file_handle, "Preset: " + preset_name);
        FileWrite(file_handle, "Generated: " + TimeToString(TimeCurrent()));
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== EA PARAMETERS ===");
        FileWrite(file_handle, "LotSize=" + DoubleToString(config.lot_size, 2));
        FileWrite(file_handle, "RiskPercent=" + DoubleToString(config.risk_percent, 1));
        FileWrite(file_handle, "StopLoss=" + IntegerToString(config.stop_loss));
        FileWrite(file_handle, "TakeProfit=" + IntegerToString(config.take_profit));
        FileWrite(file_handle, "UseTrailingStop=" + (config.use_trailing_stop ? "true" : "false"));
        FileWrite(file_handle, "TrailingStop=" + IntegerToString(config.trailing_stop));
        FileWrite(file_handle, "TrailingStep=" + IntegerToString(config.trailing_step));
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== SCALPING PARAMETERS ===");
        FileWrite(file_handle, "RSI_Period=" + IntegerToString(config.rsi_period));
        FileWrite(file_handle, "RSI_Oversold=" + IntegerToString(config.rsi_oversold));
        FileWrite(file_handle, "RSI_Overbought=" + IntegerToString(config.rsi_overbought));
        FileWrite(file_handle, "MACD_Fast=" + IntegerToString(config.macd_fast));
        FileWrite(file_handle, "MACD_Slow=" + IntegerToString(config.macd_slow));
        FileWrite(file_handle, "MACD_Signal=" + IntegerToString(config.macd_signal));
        FileWrite(file_handle, "MaxSpread=" + DoubleToString(config.max_spread, 0));
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== TIME FILTERS ===");
        FileWrite(file_handle, "UseTimeFilter=true");
        FileWrite(file_handle, "StartHour=" + IntegerToString(config.start_hour));
        FileWrite(file_handle, "EndHour=" + IntegerToString(config.end_hour));
        FileWrite(file_handle, "TradeFriday=" + (config.trade_friday ? "true" : "false"));
        FileWrite(file_handle, "");
        FileWrite(file_handle, "=== RISK MANAGEMENT ===");
        FileWrite(file_handle, "MaxDailyLoss=" + DoubleToString(config.max_daily_loss, 1));
        FileWrite(file_handle, "MaxPositions=" + IntegerToString(config.max_positions));
        FileWrite(file_handle, "MinEquity=1000.0");
        
        FileClose(file_handle);
    }
    
    return filename;
}

//+------------------------------------------------------------------+
//| Print setup instructions                                         |
//+------------------------------------------------------------------+
void PrintSetupInstructions()
{
    Print("1. Copy all EA files to MetaTrader 5/MQL5/Experts/");
    Print("2. Compile XAUUSDScalpingEA.mq5 in MetaEditor");
    Print("3. Open XAUUSD M1 chart");
    Print("4. Attach EA to chart with generated parameters");
    Print("5. Enable AutoTrading and allow DLL imports");
    Print("6. Monitor performance using the dashboard");
    Print("7. Start with demo account for testing");
    Print("");
    Print("IMPORTANT NOTES:");
    Print("- Use ECN broker with low spreads");
    Print("- Ensure stable internet connection");
    Print("- Monitor during high volatility periods");
    Print("- Regular parameter optimization recommended");
    Print("- Never risk more than you can afford to lose");
}

//+------------------------------------------------------------------+
//| Performance expectations for each preset                         |
//+------------------------------------------------------------------+
void PrintPerformanceExpectations()
{
    Print("=== PERFORMANCE EXPECTATIONS ===");
    
    switch(OptimizationPreset)
    {
        case CONSERVATIVE:
            Print("Conservative Preset:");
            Print("- Expected Monthly Return: 3-8%");
            Print("- Expected Win Rate: 60-70%");
            Print("- Expected Max Drawdown: 5-12%");
            Print("- Risk Level: Low");
            Print("- Suitable for: Risk-averse traders");
            break;
            
        case BALANCED:
            Print("Balanced Preset:");
            Print("- Expected Monthly Return: 5-15%");
            Print("- Expected Win Rate: 55-65%");
            Print("- Expected Max Drawdown: 8-18%");
            Print("- Risk Level: Medium");
            Print("- Suitable for: Most traders");
            break;
            
        case AGGRESSIVE:
            Print("Aggressive Preset:");
            Print("- Expected Monthly Return: 8-25%");
            Print("- Expected Win Rate: 50-60%");
            Print("- Expected Max Drawdown: 12-25%");
            Print("- Risk Level: High");
            Print("- Suitable for: Experienced traders");
            break;
    }
    
    Print("");
    Print("Note: Performance may vary based on market conditions,");
    Print("broker spreads, and execution quality.");
}
