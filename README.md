# XAUUSD Scalping Expert Advisor

A professional, market-dominant Expert Advisor designed specifically for scalping XAUUSD (Gold) on MetaTrader 5.

## 🚀 Key Features

### Advanced Trading Strategy
- **Multi-timeframe analysis** (M1, M5, M15)
- **Momentum-based entries** using RSI, MACD, and EMA
- **Volatility filtering** for optimal trading conditions
- **Session-based trading** (London/NY overlap focus)
- **Price action pattern recognition**

### Sophisticated Risk Management
- **Dynamic position sizing** based on volatility
- **Kelly Criterion** lot calculation
- **Maximum daily loss protection**
- **Drawdown monitoring** and emergency stops
- **Real-time risk metrics**

### Market Analysis Engine
- **Volatility detection** using ATR
- **Trend/Range market identification**
- **Trading session recognition**
- **Support/Resistance levels**
- **Candlestick pattern analysis**

### Performance Monitoring
- **Real-time dashboard** with key metrics
- **Trade statistics** and performance tracking
- **Risk monitoring** and alerts
- **Comprehensive logging**

## 📁 File Structure

```
XAUUSD-Scalping-EA/
├── XAUUSDScalpingEA.mq5      # Main EA file
├── MarketAnalysis.mqh         # Market analysis module
├── RiskManager.mqh            # Risk management system
├── Dashboard.mqh              # Performance dashboard
├── TestingFramework.mq5       # Testing and optimization
└── README.md                  # This file
```

## ⚙️ Installation

1. **Copy Files to MT5**
   ```
   Copy all .mq5 and .mqh files to:
   MetaTrader 5/MQL5/Experts/
   ```

2. **Compile the EA**
   - Open MetaEditor (F4 in MT5)
   - Open `XAUUSDScalpingEA.mq5`
   - Press F7 to compile
   - Ensure no compilation errors

3. **Attach to Chart**
   - Open XAUUSD chart
   - Drag EA from Navigator to chart
   - Configure parameters
   - Enable AutoTrading

## 🔧 Configuration

### Trading Parameters
- **LotSize**: Fixed lot size (0 = auto-sizing)
- **RiskPercent**: Risk per trade (recommended: 1-3%)
- **StopLoss**: Stop loss in points (recommended: 30-80)
- **TakeProfit**: Take profit in points (recommended: 80-200)
- **UseTrailingStop**: Enable trailing stop functionality

### Scalping Settings
- **RSI_Period**: RSI calculation period (recommended: 14)
- **RSI_Oversold/Overbought**: RSI signal levels (30/70)
- **MACD_Fast/Slow/Signal**: MACD parameters (12/26/9)
- **MinSpread/MaxSpread**: Spread filtering (0/50 points)

### Time Filters
- **UseTimeFilter**: Enable trading time restrictions
- **StartHour/EndHour**: Trading session hours (8-17 GMT)
- **TradeFriday**: Allow Friday trading

### Risk Management
- **MaxDailyLoss**: Maximum daily loss limit ($)
- **MaxPositions**: Maximum concurrent positions
- **MinEquity**: Minimum account equity to trade

## 📊 Dashboard Features

The EA includes a real-time dashboard displaying:

- **Account Information**: Balance, Equity, P&L
- **Risk Metrics**: Daily profit, win rate, drawdown
- **Market Conditions**: Volatility, trend, session
- **Trading Statistics**: Total trades, open positions
- **Technical Data**: Current spread, ATR values

## 🧪 Testing & Optimization

### Backtesting
1. Use the included `TestingFramework.mq5`
2. Set test parameters and date range
3. Run comprehensive backtests
4. Analyze performance reports

### Optimization
- Enable parameter optimization in testing framework
- Test RSI periods: 10-20
- Test Stop Loss: 30-80 points
- Test Take Profit: 80-200 points
- Evaluate multiple timeframes

### Performance Metrics
- **Profit Factor**: > 1.5 recommended
- **Win Rate**: Target 55-65%
- **Max Drawdown**: < 15% recommended
- **Sharpe Ratio**: > 1.0 preferred

## 💡 Trading Strategy Details

### Entry Conditions (BUY)
1. RSI oversold and turning up
2. MACD bullish crossover or momentum
3. Price above fast EMA or EMA trend bullish
4. High volatility conditions
5. Favorable trading session
6. Optional: Bullish engulfing pattern

### Entry Conditions (SELL)
1. RSI overbought and turning down
2. MACD bearish crossover or momentum
3. Price below fast EMA or EMA trend bearish
4. High volatility conditions
5. Favorable trading session
6. Optional: Bearish engulfing pattern

### Exit Strategy
- **Take Profit**: Fixed TP or trailing stop
- **Stop Loss**: Fixed SL with breakeven move
- **Trailing Stop**: Dynamic profit protection
- **Time-based**: Session end closure

## ⚠️ Risk Warnings

1. **High-Risk Strategy**: Scalping involves frequent trading
2. **Spread Sensitivity**: Monitor broker spreads carefully
3. **Market Conditions**: Performance varies with volatility
4. **Capital Requirements**: Minimum $1000 recommended
5. **Demo Testing**: Always test thoroughly before live trading

## 🔧 Troubleshooting

### Common Issues
- **No Trades**: Check time filters and market conditions
- **High Drawdown**: Reduce risk percentage or lot size
- **Compilation Errors**: Ensure all .mqh files are present
- **Dashboard Not Showing**: Check object permissions

### Performance Optimization
- Use VPS for consistent execution
- Choose ECN broker with low spreads
- Monitor during high volatility periods
- Regular parameter optimization

## 📈 Expected Performance

### Realistic Expectations
- **Monthly Return**: 5-15% (depending on risk settings)
- **Win Rate**: 55-65%
- **Max Drawdown**: 10-20%
- **Trades per Day**: 5-20 (depending on market conditions)

### Optimal Conditions
- **Timeframe**: M1 primary, M5 confirmation
- **Sessions**: London/NY overlap (13:00-17:00 GMT)
- **Volatility**: High ATR periods
- **Spread**: < 3 pips average

## 🛠️ Customization

The EA is designed with modular architecture for easy customization:

- **MarketAnalysis.mqh**: Add new indicators or patterns
- **RiskManager.mqh**: Modify risk calculations
- **Dashboard.mqh**: Customize display elements
- **Main EA**: Adjust signal logic and filters

## 📞 Support

For questions, issues, or customization requests:
- Review the code comments for detailed explanations
- Test thoroughly in demo environment
- Monitor performance metrics regularly
- Keep risk management as top priority

## 📄 License

This Expert Advisor is provided for educational and trading purposes. Use at your own risk and ensure compliance with your broker's terms and local regulations.

---

**Disclaimer**: Trading forex and CFDs involves significant risk and may not be suitable for all investors. Past performance is not indicative of future results. Always trade responsibly and never risk more than you can afford to lose.
