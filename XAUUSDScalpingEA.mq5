//+------------------------------------------------------------------+
//|                                           XAUUSDScalpingEA.mq5 |
//|                                    Professional XAUUSD Scalper |
//|                                             Market Dominant EA |
//+------------------------------------------------------------------+
#property copyright "Market Dominant EA"
#property link      ""
#property version   "1.00"
#property description "Advanced XAUUSD Scalping Expert Advisor"

#include "MarketAnalysis.mqh"
#include "RiskManager.mqh"
#include "Dashboard.mqh"

//--- Input parameters
input group "=== TRADING SETTINGS ==="
input double   LotSize = 0.01;                    // Fixed lot size (0 = auto)
input double   RiskPercent = 2.0;                 // Risk per trade (%)
input int      StopLoss = 50;                     // Stop Loss (points)
input int      TakeProfit = 150;                  // Take Profit (points)
input bool     UseTrailingStop = true;            // Enable trailing stop
input int      TrailingStop = 30;                 // Trailing stop (points)
input int      TrailingStep = 10;                 // Trailing step (points)

input group "=== SCALPING PARAMETERS ==="
input int      RSI_Period = 14;                   // RSI period
input int      RSI_Oversold = 30;                 // RSI oversold level
input int      RSI_Overbought = 70;               // RSI overbought level
input int      MACD_Fast = 12;                    // MACD fast EMA
input int      MACD_Slow = 26;                    // MACD slow EMA
input int      MACD_Signal = 9;                   // MACD signal line
input double   MinSpread = 0;                     // Minimum spread (points)
input double   MaxSpread = 50;                    // Maximum spread (points)

input group "=== TIME FILTERS ==="
input bool     UseTimeFilter = true;              // Enable time filter
input int      StartHour = 8;                     // Start trading hour
input int      EndHour = 17;                      // End trading hour
input bool     TradeFriday = false;               // Trade on Friday

input group "=== RISK MANAGEMENT ==="
input double   MaxDailyLoss = 100.0;              // Max daily loss ($)
input int      MaxPositions = 1;                  // Maximum open positions
input double   MinEquity = 1000.0;                // Minimum equity to trade

//--- Global variables
int            rsi_handle;
int            macd_handle;
int            ema_fast_handle;
int            ema_slow_handle;
double         daily_profit = 0.0;
datetime       last_trade_time = 0;
int            magic_number = 123456;
CMarketAnalysis *market_analysis;
CRiskManager   *risk_manager;
CDashboard     *dashboard;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize indicators
    rsi_handle = iRSI(_Symbol, PERIOD_M1, RSI_Period, PRICE_CLOSE);
    macd_handle = iMACD(_Symbol, PERIOD_M1, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);
    ema_fast_handle = iMA(_Symbol, PERIOD_M5, 21, 0, MODE_EMA, PRICE_CLOSE);
    ema_slow_handle = iMA(_Symbol, PERIOD_M5, 50, 0, MODE_EMA, PRICE_CLOSE);
    
    if(rsi_handle == INVALID_HANDLE || macd_handle == INVALID_HANDLE || 
       ema_fast_handle == INVALID_HANDLE || ema_slow_handle == INVALID_HANDLE)
    {
        Print("Error creating indicators");
        return INIT_FAILED;
    }
    
    // Initialize market analysis
    market_analysis = new CMarketAnalysis(_Symbol, PERIOD_M1);

    // Initialize risk manager
    risk_manager = new CRiskManager(MaxDailyLoss, 20.0, RiskPercent, MaxPositions);

    // Initialize dashboard
    dashboard = new CDashboard(_Symbol, ChartID());
    dashboard.CreateDashboard();

    // Reset daily profit at start
    ResetDailyProfit();

    Print("XAUUSD Scalping EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    IndicatorRelease(rsi_handle);
    IndicatorRelease(macd_handle);
    IndicatorRelease(ema_fast_handle);
    IndicatorRelease(ema_slow_handle);

    // Clean up market analysis
    if(market_analysis != NULL)
    {
        delete market_analysis;
        market_analysis = NULL;
    }

    // Clean up risk manager
    if(risk_manager != NULL)
    {
        delete risk_manager;
        risk_manager = NULL;
    }

    // Clean up dashboard
    if(dashboard != NULL)
    {
        delete dashboard;
        dashboard = NULL;
    }

    Print("XAUUSD Scalping EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, PERIOD_M1, 0);
    
    if(current_bar_time == last_bar_time)
        return;
    last_bar_time = current_bar_time;
    
    // Update daily profit
    UpdateDailyProfit();
    
    // Check risk management
    if(!CheckRiskManagement())
        return;
    
    // Check time filter
    if(!CheckTimeFilter())
        return;
    
    // Check spread
    if(!CheckSpread())
        return;

    // Check market conditions
    if(!CheckMarketConditions())
        return;

    // Manage existing positions
    ManagePositions();

    // Check for new trading signals
    if(PositionsTotal() < MaxPositions)
    {
        CheckTradingSignals();
    }

    // Update dashboard every 10 ticks
    static int tick_counter = 0;
    tick_counter++;
    if(tick_counter >= 10)
    {
        if(dashboard != NULL)
            dashboard.UpdateDashboard(risk_manager, market_analysis);
        tick_counter = 0;
    }
}

//+------------------------------------------------------------------+
//| Check risk management conditions                                 |
//+------------------------------------------------------------------+
bool CheckRiskManagement()
{
    // Check daily loss limit
    if(daily_profit <= -MaxDailyLoss)
    {
        Print("Daily loss limit reached: ", daily_profit);
        return false;
    }
    
    // Check minimum equity
    if(AccountInfoDouble(ACCOUNT_EQUITY) < MinEquity)
    {
        Print("Equity below minimum: ", AccountInfoDouble(ACCOUNT_EQUITY));
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check time filter                                               |
//+------------------------------------------------------------------+
bool CheckTimeFilter()
{
    if(!UseTimeFilter)
        return true;
    
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    // Check Friday trading
    if(!TradeFriday && dt.day_of_week == 5)
        return false;
    
    // Check trading hours
    if(dt.hour < StartHour || dt.hour >= EndHour)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check spread conditions                                          |
//+------------------------------------------------------------------+
bool CheckSpread()
{
    double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    
    if(spread < MinSpread || spread > MaxSpread)
    {
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check market conditions for trading                              |
//+------------------------------------------------------------------+
bool CheckMarketConditions()
{
    if(market_analysis == NULL)
        return true;

    // Only trade during high volatility periods
    if(!market_analysis.IsHighVolatility())
        return false;

    // Prefer trading during session overlaps
    if(!market_analysis.IsSessionOverlap() && !market_analysis.IsLondonSession() && !market_analysis.IsNewYorkSession())
        return false;

    // Avoid trading during low volatility periods
    if(!market_analysis.IsVolatilityIncreasing())
        return false;

    return true;
}

//+------------------------------------------------------------------+
//| Update daily profit calculation                                  |
//+------------------------------------------------------------------+
void UpdateDailyProfit()
{
    static datetime last_day = 0;
    datetime current_day = (datetime)(TimeCurrent() / 86400) * 86400;
    
    if(current_day != last_day)
    {
        ResetDailyProfit();
        last_day = current_day;
    }
    
    // Calculate current day profit
    daily_profit = 0.0;
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
               PositionGetInteger(POSITION_MAGIC) == magic_number)
            {
                daily_profit += PositionGetDouble(POSITION_PROFIT);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Reset daily profit                                              |
//+------------------------------------------------------------------+
void ResetDailyProfit()
{
    daily_profit = 0.0;
}

//+------------------------------------------------------------------+
//| Calculate optimal lot size                                       |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    if(LotSize > 0)
        return LotSize;

    if(risk_manager != NULL)
    {
        // Use advanced risk management
        double atr_value = 0;
        if(market_analysis != NULL)
            atr_value = market_analysis.GetATR();

        if(atr_value > 0)
            return risk_manager.CalculateVolatilityAdjustedLotSize(atr_value);
        else
            return risk_manager.CalculateOptimalLotSize(StopLoss, AccountInfoDouble(ACCOUNT_BALANCE));
    }

    // Fallback to basic calculation
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = balance * RiskPercent / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double lot_size = risk_amount / (StopLoss * tick_value);

    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

    return lot_size;
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
    // Get indicator values
    double rsi_current = GetRSIValue(0);
    double rsi_previous = GetRSIValue(1);

    double macd_main[], macd_signal[];
    ArraySetAsSeries(macd_main, true);
    ArraySetAsSeries(macd_signal, true);

    if(CopyBuffer(macd_handle, 0, 0, 3, macd_main) < 3 ||
       CopyBuffer(macd_handle, 1, 0, 3, macd_signal) < 3)
        return;

    double ema_fast = GetEMAValue(ema_fast_handle, 0);
    double ema_slow = GetEMAValue(ema_slow_handle, 0);

    if(rsi_current == EMPTY_VALUE || ema_fast == EMPTY_VALUE || ema_slow == EMPTY_VALUE)
        return;

    // Current price data
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Check for BUY signal
    if(CheckBuySignal(rsi_current, rsi_previous, macd_main, macd_signal, ema_fast, ema_slow, ask))
    {
        OpenBuyPosition(ask);
    }
    // Check for SELL signal
    else if(CheckSellSignal(rsi_current, rsi_previous, macd_main, macd_signal, ema_fast, ema_slow, bid))
    {
        OpenSellPosition(bid);
    }
}

//+------------------------------------------------------------------+
//| Check BUY signal conditions                                      |
//+------------------------------------------------------------------+
bool CheckBuySignal(double rsi_current, double rsi_previous, double &macd_main[], double &macd_signal[],
                    double ema_fast, double ema_slow, double price)
{
    // RSI oversold and turning up
    bool rsi_signal = (rsi_previous <= RSI_Oversold && rsi_current > rsi_previous);

    // MACD bullish crossover or above signal line
    bool macd_signal_check = (macd_main[0] > macd_signal[0] && macd_main[1] <= macd_signal[1]) ||
                            (macd_main[0] > macd_signal[0] && macd_main[0] > macd_main[1]);

    // EMA trend confirmation
    bool ema_trend = ema_fast > ema_slow;

    // Price above fast EMA
    bool price_position = price > ema_fast;

    // Momentum confirmation
    bool momentum = macd_main[0] > macd_main[1];

    // Market analysis confirmation
    bool market_confirmation = true;
    if(market_analysis != NULL)
    {
        market_confirmation = market_analysis.IsTrendingMarket() || market_analysis.IsBullishEngulfing();
    }

    return rsi_signal && macd_signal_check && (ema_trend || price_position) && momentum && market_confirmation;
}

//+------------------------------------------------------------------+
//| Check SELL signal conditions                                     |
//+------------------------------------------------------------------+
bool CheckSellSignal(double rsi_current, double rsi_previous, double &macd_main[], double &macd_signal[],
                     double ema_fast, double ema_slow, double price)
{
    // RSI overbought and turning down
    bool rsi_signal = (rsi_previous >= RSI_Overbought && rsi_current < rsi_previous);

    // MACD bearish crossover or below signal line
    bool macd_signal_check = (macd_main[0] < macd_signal[0] && macd_main[1] >= macd_signal[1]) ||
                            (macd_main[0] < macd_signal[0] && macd_main[0] < macd_main[1]);

    // EMA trend confirmation
    bool ema_trend = ema_fast < ema_slow;

    // Price below fast EMA
    bool price_position = price < ema_fast;

    // Momentum confirmation
    bool momentum = macd_main[0] < macd_main[1];

    // Market analysis confirmation
    bool market_confirmation = true;
    if(market_analysis != NULL)
    {
        market_confirmation = market_analysis.IsTrendingMarket() || market_analysis.IsBearishEngulfing();
    }

    return rsi_signal && macd_signal_check && (ema_trend || price_position) && momentum && market_confirmation;
}

//+------------------------------------------------------------------+
//| Open BUY position                                                |
//+------------------------------------------------------------------+
void OpenBuyPosition(double price)
{
    double lot_size = CalculateLotSize();
    double sl = price - StopLoss * _Point;
    double tp = price + TakeProfit * _Point;

    // Normalize prices
    sl = NormalizeDouble(sl, _Digits);
    tp = NormalizeDouble(tp, _Digits);

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lot_size;
    request.type = ORDER_TYPE_BUY;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = magic_number;
    request.comment = "XAUUSD Scalp BUY";
    request.deviation = 3;

    if(OrderSend(request, result))
    {
        Print("BUY order opened: ", result.order, " at price ", price);
        last_trade_time = TimeCurrent();
    }
    else
    {
        Print("Error opening BUY order: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| Open SELL position                                               |
//+------------------------------------------------------------------+
void OpenSellPosition(double price)
{
    double lot_size = CalculateLotSize();
    double sl = price + StopLoss * _Point;
    double tp = price - TakeProfit * _Point;

    // Normalize prices
    sl = NormalizeDouble(sl, _Digits);
    tp = NormalizeDouble(tp, _Digits);

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lot_size;
    request.type = ORDER_TYPE_SELL;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = magic_number;
    request.comment = "XAUUSD Scalp SELL";
    request.deviation = 3;

    if(OrderSend(request, result))
    {
        Print("SELL order opened: ", result.order, " at price ", price);
        last_trade_time = TimeCurrent();
    }
    else
    {
        Print("Error opening SELL order: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManagePositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == magic_number)
            {
                if(UseTrailingStop)
                {
                    TrailingStop(PositionGetTicket(i));
                }

                // Check for breakeven move
                CheckBreakeven(PositionGetTicket(i));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Trailing stop function                                           |
//+------------------------------------------------------------------+
void TrailingStop(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return;

    double current_price;
    double current_sl = PositionGetDouble(POSITION_SL);
    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    if(pos_type == POSITION_TYPE_BUY)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        double new_sl = current_price - TrailingStop * _Point;

        if(new_sl > current_sl + TrailingStep * _Point && new_sl > open_price)
        {
            ModifyPosition(ticket, NormalizeDouble(new_sl, _Digits), PositionGetDouble(POSITION_TP));
        }
    }
    else if(pos_type == POSITION_TYPE_SELL)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        double new_sl = current_price + TrailingStop * _Point;

        if((current_sl == 0 || new_sl < current_sl - TrailingStep * _Point) && new_sl < open_price)
        {
            ModifyPosition(ticket, NormalizeDouble(new_sl, _Digits), PositionGetDouble(POSITION_TP));
        }
    }
}

//+------------------------------------------------------------------+
//| Check and move to breakeven                                      |
//+------------------------------------------------------------------+
void CheckBreakeven(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return;

    double current_price;
    double current_sl = PositionGetDouble(POSITION_SL);
    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    double breakeven_distance = 20 * _Point; // Move to BE when 20 points in profit

    if(pos_type == POSITION_TYPE_BUY)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

        if(current_price >= open_price + breakeven_distance && current_sl < open_price)
        {
            ModifyPosition(ticket, NormalizeDouble(open_price + 5 * _Point, _Digits), PositionGetDouble(POSITION_TP));
        }
    }
    else if(pos_type == POSITION_TYPE_SELL)
    {
        current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

        if(current_price <= open_price - breakeven_distance && (current_sl == 0 || current_sl > open_price))
        {
            ModifyPosition(ticket, NormalizeDouble(open_price - 5 * _Point, _Digits), PositionGetDouble(POSITION_TP));
        }
    }
}

//+------------------------------------------------------------------+
//| Modify position                                                  |
//+------------------------------------------------------------------+
void ModifyPosition(ulong ticket, double sl, double tp)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_SLTP;
    request.position = ticket;
    request.sl = sl;
    request.tp = tp;

    if(!OrderSend(request, result))
    {
        Print("Error modifying position: ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Get RSI value                                                    |
//+------------------------------------------------------------------+
double GetRSIValue(int shift)
{
    double rsi_buffer[];
    ArraySetAsSeries(rsi_buffer, true);

    if(CopyBuffer(rsi_handle, 0, shift, 1, rsi_buffer) < 1)
        return EMPTY_VALUE;

    return rsi_buffer[0];
}

//+------------------------------------------------------------------+
//| Get EMA value                                                    |
//+------------------------------------------------------------------+
double GetEMAValue(int handle, int shift)
{
    double ema_buffer[];
    ArraySetAsSeries(ema_buffer, true);

    if(CopyBuffer(handle, 0, shift, 1, ema_buffer) < 1)
        return EMPTY_VALUE;

    return ema_buffer[0];
}
