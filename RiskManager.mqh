//+------------------------------------------------------------------+
//|                                                  RiskManager.mqh |
//|                                    Advanced Risk Management      |
//|                                             For XAUUSD Scalping |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Risk Management Class                                            |
//+------------------------------------------------------------------+
class CRiskManager
{
private:
    double            m_max_daily_loss;
    double            m_max_drawdown_percent;
    double            m_risk_per_trade;
    int               m_max_positions;
    double            m_account_balance;
    double            m_daily_profit;
    double            m_max_equity;
    datetime          m_last_reset_day;
    
    // Risk metrics
    double            m_win_rate;
    double            m_avg_win;
    double            m_avg_loss;
    int               m_total_trades;
    int               m_winning_trades;
    
public:
    // Constructor
    CRiskManager(double max_daily_loss, double max_drawdown, double risk_per_trade, int max_positions);
    
    // Risk validation
    bool              CanOpenPosition(double lot_size, double stop_loss);
    bool              IsWithinDailyLoss();
    bool              IsWithinDrawdown();
    bool              IsPositionLimitReached();
    
    // Position sizing
    double            CalculateOptimalLotSize(double stop_loss_points, double account_balance);
    double            CalculateKellyLotSize();
    double            CalculateVolatilityAdjustedLotSize(double atr_value);
    
    // Risk monitoring
    void              UpdateDailyProfit();
    void              UpdateTradeStatistics(bool is_win, double profit);
    void              ResetDailyMetrics();
    
    // Emergency controls
    bool              ShouldStopTrading();
    void              CloseAllPositions();
    
    // Getters
    double            GetDailyProfit() { return m_daily_profit; }
    double            GetWinRate() { return m_win_rate; }
    double            GetCurrentDrawdown();
    double            GetRiskReward();
    
    // Risk reporting
    string            GetRiskReport();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CRiskManager::CRiskManager(double max_daily_loss, double max_drawdown, double risk_per_trade, int max_positions)
{
    m_max_daily_loss = max_daily_loss;
    m_max_drawdown_percent = max_drawdown;
    m_risk_per_trade = risk_per_trade;
    m_max_positions = max_positions;
    m_account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    m_daily_profit = 0.0;
    m_max_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    m_last_reset_day = TimeCurrent();
    
    // Initialize statistics
    m_win_rate = 0.0;
    m_avg_win = 0.0;
    m_avg_loss = 0.0;
    m_total_trades = 0;
    m_winning_trades = 0;
}

//+------------------------------------------------------------------+
//| Check if can open new position                                   |
//+------------------------------------------------------------------+
bool CRiskManager::CanOpenPosition(double lot_size, double stop_loss)
{
    // Check daily loss limit
    if(!IsWithinDailyLoss())
        return false;
    
    // Check drawdown limit
    if(!IsWithinDrawdown())
        return false;
    
    // Check position limit
    if(IsPositionLimitReached())
        return false;
    
    // Check if risk per trade is acceptable
    double risk_amount = lot_size * stop_loss * SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double max_risk = AccountInfoDouble(ACCOUNT_BALANCE) * m_risk_per_trade / 100.0;
    
    if(risk_amount > max_risk)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if within daily loss limit                                 |
//+------------------------------------------------------------------+
bool CRiskManager::IsWithinDailyLoss()
{
    UpdateDailyProfit();
    return (m_daily_profit > -m_max_daily_loss);
}

//+------------------------------------------------------------------+
//| Check if within drawdown limit                                   |
//+------------------------------------------------------------------+
bool CRiskManager::IsWithinDrawdown()
{
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double drawdown_percent = (m_max_equity - current_equity) / m_max_equity * 100.0;
    
    return (drawdown_percent < m_max_drawdown_percent);
}

//+------------------------------------------------------------------+
//| Check if position limit reached                                  |
//+------------------------------------------------------------------+
bool CRiskManager::IsPositionLimitReached()
{
    return (PositionsTotal() >= m_max_positions);
}

//+------------------------------------------------------------------+
//| Calculate optimal lot size based on risk                         |
//+------------------------------------------------------------------+
double CRiskManager::CalculateOptimalLotSize(double stop_loss_points, double account_balance)
{
    double risk_amount = account_balance * m_risk_per_trade / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double lot_size = risk_amount / (stop_loss_points * tick_value);
    
    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;
    
    return lot_size;
}

//+------------------------------------------------------------------+
//| Calculate Kelly criterion lot size                               |
//+------------------------------------------------------------------+
double CRiskManager::CalculateKellyLotSize()
{
    if(m_total_trades < 10 || m_avg_loss == 0)
        return CalculateOptimalLotSize(50, AccountInfoDouble(ACCOUNT_BALANCE));
    
    double kelly_percent = (m_win_rate * m_avg_win - (1 - m_win_rate) * m_avg_loss) / m_avg_loss;
    kelly_percent = MathMax(0.01, MathMin(0.25, kelly_percent)); // Limit Kelly to 1-25%
    
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * kelly_percent;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double lot_size = risk_amount / (50 * tick_value); // Assuming 50 point stop loss
    
    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;
    
    return lot_size;
}

//+------------------------------------------------------------------+
//| Calculate volatility adjusted lot size                           |
//+------------------------------------------------------------------+
double CRiskManager::CalculateVolatilityAdjustedLotSize(double atr_value)
{
    double base_lot = CalculateOptimalLotSize(50, AccountInfoDouble(ACCOUNT_BALANCE));
    double avg_atr = atr_value; // This should be average ATR over period
    
    // Adjust lot size based on volatility
    double volatility_multiplier = 1.0;
    if(atr_value > avg_atr * 1.5)
        volatility_multiplier = 0.7; // Reduce size in high volatility
    else if(atr_value < avg_atr * 0.7)
        volatility_multiplier = 1.3; // Increase size in low volatility
    
    return base_lot * volatility_multiplier;
}

//+------------------------------------------------------------------+
//| Update daily profit                                              |
//+------------------------------------------------------------------+
void CRiskManager::UpdateDailyProfit()
{
    // Check if new day
    datetime current_day = (datetime)(TimeCurrent() / 86400) * 86400;
    if(current_day != (datetime)(m_last_reset_day / 86400) * 86400)
    {
        ResetDailyMetrics();
        m_last_reset_day = current_day;
    }
    
    // Calculate current day profit
    m_daily_profit = 0.0;
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol)
            {
                m_daily_profit += PositionGetDouble(POSITION_PROFIT);
            }
        }
    }
    
    // Update max equity
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    if(current_equity > m_max_equity)
        m_max_equity = current_equity;
}

//+------------------------------------------------------------------+
//| Update trade statistics                                          |
//+------------------------------------------------------------------+
void CRiskManager::UpdateTradeStatistics(bool is_win, double profit)
{
    m_total_trades++;
    
    if(is_win)
    {
        m_winning_trades++;
        m_avg_win = (m_avg_win * (m_winning_trades - 1) + profit) / m_winning_trades;
    }
    else
    {
        int losing_trades = m_total_trades - m_winning_trades;
        m_avg_loss = (m_avg_loss * (losing_trades - 1) + MathAbs(profit)) / losing_trades;
    }
    
    m_win_rate = (double)m_winning_trades / m_total_trades;
}

//+------------------------------------------------------------------+
//| Reset daily metrics                                              |
//+------------------------------------------------------------------+
void CRiskManager::ResetDailyMetrics()
{
    m_daily_profit = 0.0;
}

//+------------------------------------------------------------------+
//| Check if should stop trading                                     |
//+------------------------------------------------------------------+
bool CRiskManager::ShouldStopTrading()
{
    return (!IsWithinDailyLoss() || !IsWithinDrawdown());
}

//+------------------------------------------------------------------+
//| Get current drawdown percentage                                  |
//+------------------------------------------------------------------+
double CRiskManager::GetCurrentDrawdown()
{
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    return (m_max_equity - current_equity) / m_max_equity * 100.0;
}

//+------------------------------------------------------------------+
//| Get risk reward ratio                                            |
//+------------------------------------------------------------------+
double CRiskManager::GetRiskReward()
{
    if(m_avg_loss == 0)
        return 0.0;
    
    return m_avg_win / m_avg_loss;
}
