//+------------------------------------------------------------------+
//|                                              MarketAnalysis.mqh |
//|                                    Advanced Market Analysis     |
//|                                             For XAUUSD Scalping |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Market Analysis Class                                            |
//+------------------------------------------------------------------+
class CMarketAnalysis
{
private:
    int               m_atr_handle;
    int               m_bb_handle;
    int               m_stoch_handle;
    string            m_symbol;
    ENUM_TIMEFRAMES   m_timeframe;
    
public:
    // Constructor
    CMarketAnalysis(string symbol, ENUM_TIMEFRAMES timeframe);
    
    // Destructor
    ~CMarketAnalysis();
    
    // Market condition analysis
    bool              IsHighVolatility();
    bool              IsTrendingMarket();
    bool              IsRangingMarket();
    double            GetMarketStrength();
    
    // Volatility analysis
    double            GetATR(int shift = 0);
    double            GetVolatilityRatio();
    bool              IsVolatilityIncreasing();
    
    // Support/Resistance levels
    double            GetNearestSupport(double price);
    double            GetNearestResistance(double price);
    bool              IsNearSupportResistance(double price, double threshold = 10);
    
    // Market session analysis
    bool              IsLondonSession();
    bool              IsNewYorkSession();
    bool              IsAsianSession();
    bool              IsSessionOverlap();
    
    // Price action analysis
    bool              IsBullishEngulfing(int shift = 1);
    bool              IsBearishEngulfing(int shift = 1);
    bool              IsHammer(int shift = 1);
    bool              IsShootingStar(int shift = 1);
    
    // Momentum analysis
    double            GetMomentumScore();
    bool              IsMomentumIncreasing();
    bool              IsDivergence();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CMarketAnalysis::CMarketAnalysis(string symbol, ENUM_TIMEFRAMES timeframe)
{
    m_symbol = symbol;
    m_timeframe = timeframe;
    
    // Initialize indicators
    m_atr_handle = iATR(m_symbol, m_timeframe, 14);
    m_bb_handle = iBands(m_symbol, m_timeframe, 20, 0, 2.0, PRICE_CLOSE);
    m_stoch_handle = iStochastic(m_symbol, m_timeframe, 14, 3, 3, MODE_SMA, STO_LOWHIGH);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CMarketAnalysis::~CMarketAnalysis()
{
    IndicatorRelease(m_atr_handle);
    IndicatorRelease(m_bb_handle);
    IndicatorRelease(m_stoch_handle);
}

//+------------------------------------------------------------------+
//| Check if market is in high volatility                           |
//+------------------------------------------------------------------+
bool CMarketAnalysis::IsHighVolatility()
{
    double atr_current = GetATR(0);
    double atr_average = 0;
    
    // Calculate average ATR over last 20 periods
    for(int i = 1; i <= 20; i++)
    {
        atr_average += GetATR(i);
    }
    atr_average /= 20;
    
    return (atr_current > atr_average * 1.5);
}

//+------------------------------------------------------------------+
//| Check if market is trending                                      |
//+------------------------------------------------------------------+
bool CMarketAnalysis::IsTrendingMarket()
{
    double bb_upper[], bb_lower[], bb_middle[];
    ArraySetAsSeries(bb_upper, true);
    ArraySetAsSeries(bb_lower, true);
    ArraySetAsSeries(bb_middle, true);
    
    if(CopyBuffer(m_bb_handle, 1, 0, 5, bb_upper) < 5 ||
       CopyBuffer(m_bb_handle, 2, 0, 5, bb_lower) < 5 ||
       CopyBuffer(m_bb_handle, 0, 0, 5, bb_middle) < 5)
        return false;
    
    // Check if Bollinger Bands are expanding
    double current_width = bb_upper[0] - bb_lower[0];
    double previous_width = bb_upper[4] - bb_lower[4];
    
    return (current_width > previous_width * 1.2);
}

//+------------------------------------------------------------------+
//| Check if market is ranging                                       |
//+------------------------------------------------------------------+
bool CMarketAnalysis::IsRangingMarket()
{
    return !IsTrendingMarket();
}

//+------------------------------------------------------------------+
//| Get ATR value                                                    |
//+------------------------------------------------------------------+
double CMarketAnalysis::GetATR(int shift = 0)
{
    double atr_buffer[];
    ArraySetAsSeries(atr_buffer, true);
    
    if(CopyBuffer(m_atr_handle, 0, shift, 1, atr_buffer) < 1)
        return 0;
    
    return atr_buffer[0];
}

//+------------------------------------------------------------------+
//| Get volatility ratio                                             |
//+------------------------------------------------------------------+
double CMarketAnalysis::GetVolatilityRatio()
{
    double atr_current = GetATR(0);
    double atr_previous = GetATR(1);
    
    if(atr_previous == 0)
        return 1.0;
    
    return atr_current / atr_previous;
}

//+------------------------------------------------------------------+
//| Check if volatility is increasing                                |
//+------------------------------------------------------------------+
bool CMarketAnalysis::IsVolatilityIncreasing()
{
    return (GetVolatilityRatio() > 1.1);
}

//+------------------------------------------------------------------+
//| Check if it's London session                                     |
//+------------------------------------------------------------------+
bool CMarketAnalysis::IsLondonSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeGMT(), dt);
    
    return (dt.hour >= 8 && dt.hour < 17);
}

//+------------------------------------------------------------------+
//| Check if it's New York session                                   |
//+------------------------------------------------------------------+
bool CMarketAnalysis::IsNewYorkSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeGMT(), dt);
    
    return (dt.hour >= 13 && dt.hour < 22);
}

//+------------------------------------------------------------------+
//| Check if it's Asian session                                      |
//+------------------------------------------------------------------+
bool CMarketAnalysis::IsAsianSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeGMT(), dt);
    
    return (dt.hour >= 23 || dt.hour < 8);
}

//+------------------------------------------------------------------+
//| Check if it's session overlap                                    |
//+------------------------------------------------------------------+
bool CMarketAnalysis::IsSessionOverlap()
{
    return (IsLondonSession() && IsNewYorkSession());
}

//+------------------------------------------------------------------+
//| Check for bullish engulfing pattern                              |
//+------------------------------------------------------------------+
bool CMarketAnalysis::IsBullishEngulfing(int shift = 1)
{
    double open[], high[], low[], close[];
    ArraySetAsSeries(open, true);
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    
    if(CopyOpen(m_symbol, m_timeframe, shift, 2, open) < 2 ||
       CopyHigh(m_symbol, m_timeframe, shift, 2, high) < 2 ||
       CopyLow(m_symbol, m_timeframe, shift, 2, low) < 2 ||
       CopyClose(m_symbol, m_timeframe, shift, 2, close) < 2)
        return false;
    
    // Previous candle is bearish
    bool prev_bearish = close[1] < open[1];
    
    // Current candle is bullish
    bool curr_bullish = close[0] > open[0];
    
    // Current candle engulfs previous
    bool engulfs = open[0] < close[1] && close[0] > open[1];
    
    return (prev_bearish && curr_bullish && engulfs);
}

//+------------------------------------------------------------------+
//| Check for bearish engulfing pattern                              |
//+------------------------------------------------------------------+
bool CMarketAnalysis::IsBearishEngulfing(int shift = 1)
{
    double open[], high[], low[], close[];
    ArraySetAsSeries(open, true);
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    
    if(CopyOpen(m_symbol, m_timeframe, shift, 2, open) < 2 ||
       CopyHigh(m_symbol, m_timeframe, shift, 2, high) < 2 ||
       CopyLow(m_symbol, m_timeframe, shift, 2, low) < 2 ||
       CopyClose(m_symbol, m_timeframe, shift, 2, close) < 2)
        return false;
    
    // Previous candle is bullish
    bool prev_bullish = close[1] > open[1];
    
    // Current candle is bearish
    bool curr_bearish = close[0] < open[0];
    
    // Current candle engulfs previous
    bool engulfs = open[0] > close[1] && close[0] < open[1];
    
    return (prev_bullish && curr_bearish && engulfs);
}

//+------------------------------------------------------------------+
//| Get momentum score                                               |
//+------------------------------------------------------------------+
double CMarketAnalysis::GetMomentumScore()
{
    double stoch_main[], stoch_signal[];
    ArraySetAsSeries(stoch_main, true);
    ArraySetAsSeries(stoch_signal, true);
    
    if(CopyBuffer(m_stoch_handle, 0, 0, 1, stoch_main) < 1 ||
       CopyBuffer(m_stoch_handle, 1, 0, 1, stoch_signal) < 1)
        return 50.0;
    
    return stoch_main[0];
}
